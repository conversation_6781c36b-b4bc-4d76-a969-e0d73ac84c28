<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Kaira - Checkout</title>
    <meta name="description" content="Checkout page for Kaira eCommerce Template">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="apple-touch-icon" href="../apple-touch-icon.png">
    <!-- Place favicon.ico in the root directory -->
    <link rel="stylesheet" href="../css/normalize.css">
    <link rel="stylesheet" href="../css/vendor.css">
    <link rel="stylesheet" href="../css/swiper-bundle.min.css">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <script src="../js/modernizr.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/checkout.css">
</head>
<body>
    

    <!-- Offcanvas Cart -->
    <div class="offcanvas offcanvas-end" data-bs-scroll="true" tabindex="-1" id="offcanvasCart" aria-labelledby="My Cart">
      <div class="offcanvas-header justify-content-center">
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="order-md-last">
          <h4 class="d-flex justify-content-between align-items-center mb-3">
            <span class="text-primary">Your cart</span>
            <span class="badge bg-primary rounded-pill cart-count">(0)</span>
          </h4>
          <ul class="list-group mb-3" id="offcanvasCartList">
            <!-- Cart items will be rendered here by cart.js -->
          </ul>
          <button class="w-100 btn btn-primary btn-lg" onclick="window.location.href='checkout.html'">Continue to Checkout</button>
        </div>
      </div>
    </div>
    
    <main>
        <section class="checkout-process padding-large">
            <div class="container mt-4">
                <!-- Billing Address + Cart in a row -->
                <div class="row">
                    <div class="col-md-6 mb-4"> <!-- Billing Address -->
                        <div class="card shadow-sm">
                            <div class="card-header">
                                <h5>Billing Address</h5>
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName">
                                            <div class="invalid-feedback">
                                                Valid first name is required.
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName">
                                            <div class="invalid-feedback">
                                                Valid last name is required.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" placeholder="<EMAIL>">
                                        <div class="invalid-feedback">
                                            Please enter a valid email address for shipping updates.
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="address" class="form-label">Address</label>
                                        <input type="text" class="form-control" id="address" placeholder="1234 Main St">
                                        <div class="invalid-feedback">
                                            Please enter your shipping address.
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="address2" class="form-label">Address 2 <span class="text-muted">(Optional)</span></label>
                                        <input type="text" class="form-control" id="address2" placeholder="Apartment or suite">
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-5">
                                            <label for="country" class="form-label">Country</label>
                                            <select class="form-select" id="country">
                                                <option value="">Choose...</option>
                                                <option>United States</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a valid country.
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="state" class="form-label">State</label>
                                            <select class="form-select" id="state">
                                                <option value="">Choose...</option>
                                                <option>California</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please provide a valid state.
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="zip" class="form-label">Zip</label>
                                            <input type="text" class="form-control" id="zip">
                                            <div class="invalid-feedback">
                                                Zip code required.
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="my-4">
                                    <div class="form-check mb-3">
                                        <input type="checkbox" class="form-check-input" id="same-address">
                                        <label class="form-check-label" for="same-address">Shipping address is the same as my billing address</label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input type="checkbox" class="form-check-input" id="save-info">
                                        <label class="form-check-label" for="save-info">Save this information for next time</label>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Section -->
                    <div class="col-md-6 mb-4"> <!-- Cart -->
                        <div class="card shadow-sm">
                            <div class="card-header">
                                <h5>Your Cart</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group mb-3" id="checkoutCartList">
                                    <!-- Cart items will be dynamically loaded here by JavaScript -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header">
                                <h5>Payment Method</h5>
                            </div>
                            <div class="card-body">
                                <div class="my-3">
                                    <div class="form-check">
                                        <input id="credit" name="paymentMethod" type="radio" class="form-check-input" checked>
                                        <label class="form-check-label" for="credit">Credit card</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="debit" name="paymentMethod" type="radio" class="form-check-input">
                                        <label class="form-check-label" for="debit">Debit card</label>
                                    </div>
                                    <div class="form-check">
                                        <input id="paypal" name="paymentMethod" type="radio" class="form-check-input">
                                        <label class="form-check-label" for="paypal">PayPal</label>
                                    </div>
                                </div>
                                <div class="row gy-3">
                                    <div class="col-md-6">
                                        <label for="cc-name" class="form-label">Name on card</label>
                                        <input type="text" class="form-control" id="cc-name" placeholder="">
                                        <small class="text-muted">Full name as displayed on card</small>
                                        <div class="invalid-feedback">
                                            Name on card is required
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="cc-number" class="form-label">Credit card number</label>
                                        <input type="text" class="form-control" id="cc-number" placeholder="">
                                        <div class="invalid-feedback">
                                            Credit card number is required
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="cc-expiration" class="form-label">Expiration</label>
                                        <input type="text" class="form-control" id="cc-expiration" placeholder="">
                                        <div class="invalid-feedback">
                                            Expiration date required
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="cc-cvv" class="form-label">CVV</label>
                                        <input type="text" class="form-control" id="cc-cvv" placeholder="">
                                        <div class="invalid-feedback">
                                            Security code required
                                        </div>
                                    </div>
                                </div>
                                <hr class="my-4">
                                <button id="stripeCheckoutBtn" class="w-100 btn btn-dark btn-lg rounded-pill" type="button" onclick="initiateStripeCheckout()">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="footer" class="bg-light py-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-4 text-center text-lg-start">
                    <p class="mb-0">&copy; 2023 Kaira. All rights reserved.</p>
                </div>
                <div class="col-lg-4 text-center py-3 py-lg-0">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item me-3">
                            <a href="#">
                                <img src="../images/visa-card.png" alt="Visa Card">
                            </a>
                        </li>
                        <li class="list-inline-item me-3">
                            <a href="#">
                                <img src="../images/master-card.png" alt="Master Card">
                            </a>
                        </li>
                        <li class="list-inline-item">
                            <a href="#">
                                <img src="../images/paypal-card.png" alt="Paypal Card">
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 text-center text-lg-end">
                    <p class="mb-0">
                        <a href="#" class="text-dark">Privacy Policy</a>
                        <span class="px-2">|</span>
                        <a href="#" class="text-dark">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/plugins.js"></script>
    <script src="../js/script.min.js"></script>
    <script src="../js/cart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
      // Initialize Stripe with your publishable key
      const stripe = Stripe("pk_test_51S1PC6KgFNfAV3y9ERp1RUYdXmQtUBqSxHbZCbmnE9zBD5VNpC3JmCBzo8d8YZuV9c6lAIrExrHAo9IFMXSRrpC500lKGsSQmP");

      // Function to initiate Stripe checkout
      async function initiateStripeCheckout() {
        const response = await fetch("http://localhost:8000/create-checkout-session", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });
        const { sessionId } = await response.json();
        
        const stripe = Stripe("pk_test_...");
        stripe.redirectToCheckout({ sessionId });
      }
    </script>

    <!-- Script to fetch and inject header using the same approach as other pages -->
    <script>
      // Fetch and inject the header
      fetch('header.html')
        .then(response => response.text())
        .then(html => {
          const placeholder = document.getElementById('header-placeholder');
          placeholder.innerHTML = html;
          
          // Fix navigation links for subpages
          const navLinks = placeholder.querySelectorAll('.navbar-nav a');
          navLinks.forEach(link => {
            // Update the Home link to point to root
            if (link.textContent.trim() === 'Home') {
              link.setAttribute('href', '../index.html');
            }
            
            // Remove "pages/" prefix from links if they have it
            const href = link.getAttribute('href');
            if (href && href.startsWith('pages/')) {
              link.setAttribute('href', href.replace('pages/', ''));
            }
          });

          // Fix user account links
          const accountLinks = placeholder.querySelectorAll('.col-lg-auto a');
          accountLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('pages/')) {
              link.setAttribute('href', href.replace('pages/', ''));
            } else if (href === 'index.html') {
              link.setAttribute('href', '../index.html');
            }
          });

          // Re-initialize Bootstrap components if needed
          if (typeof bootstrap !== 'undefined') {
            const offcanvasEl = document.getElementById('offcanvasCart');
            if (offcanvasEl) new bootstrap.Offcanvas(offcanvasEl);
          }
          // Re-initialize cart logic if needed
          if (typeof initCart === 'function') initCart();
        })
        .catch(console.error);


    </script>
</body>
</html>
