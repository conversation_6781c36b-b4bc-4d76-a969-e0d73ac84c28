// Pet items product data
const petItems = [
  {
    id: "10116269179044",
    name: "Pet Dog Compostable Poop Bags with Tie Handles Pack of 100 Green Unscented 323883 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/rsz_0_800_882a2_323883_296e642c-dc6c-475a-bda8-af08f74b6024.jpg?v=1747569136",
    description:
      "Pet Dog Compostable Poop Bags with Tie Handles Pack of 100 Green Unscented 323883 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.85,
  },
  {
    id: "8310473425060",
    name: "Pet Dog Poop Bags 22 x 30 cm Pack of 6 Rolls Assorted Colours 9981 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/IMG_8148_clipped_rev_1_2155a48b-1028-4a6c-ad2a-0b758ea8af13.jpg?v=1700005677",
    description:
      "Pet Dog Poop Bags 22 x 30 cm Pack of 6 Rolls Assorted Colours 9981 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.75,
  },
  {
    id: "10116268818596",
    name: "Pet Dog Toy Bag of 10 Tennis Balls 323984 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/rsz_0_800_365a8_323984_29676426-b313-491c-b7fd-953d4b1fe2f3.jpg?v=1747409757",
    description: "Pet Dog Toy Bag of 10 Tennis Balls 323984 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 3.5,
  },
  {
    id: "8311035723940",
    name: "Pet Dog Poop Bag with Keychain Carrier Case Assorted Colours 0044 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/4080_81d10ef8-42e3-4c86-87ab-6acd5ee8f4530.jpg?v=1700013330",
    description:
      "Pet Dog Poop Bag with Keychain Carrier Case Assorted Colours 0044 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.65,
  },
  {
    id: "8310585098404",
    name: "Foldable Pet Dog Cat Animal Carrier Bag Assorted Designs 2023 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/Dog_Bag_item_code_2023_13.jpg?v=1700007987",
    description:
      "Foldable Pet Dog Cat Animal Carrier Bag Assorted Designs 2023 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 9.95,
  },
  {
    id: "8809423962276",
    name: "Pet Dog Cat Canvas Shoulder Tote Carrier Bag with Lion Design Assorted Colours 7793 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/7793_1_1ff68ff5-ab05-44fa-a54f-6a3758ed1f9c.png?v=1747919587",
    description:
      "Pet Dog Cat Canvas Shoulder Tote Carrier Bag with Lion Design Assorted Colours 7793 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 7.95,
  },
  {
    id: "8311169745060",
    name: "Degradable Doggy Poop Bags with Tie Handles Pack of 100 (4 Rolls) 32 x 36 cm B1459 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/B1459_1.png?v=1751815899",
    description:
      "Degradable Doggy Poop Bags with Tie Handles Pack of 100 (4 Rolls) 32 x 36 cm B1459 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 1.5,
  },
  {
    id: "8310758244516",
    name: "Premium Doggy Poop Bags with Tie Handles Fragrance Pack of 100 B0355 / B0355A (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/B0355.png?v=1700010143",
    description:
      "Premium Doggy Poop Bags with Tie Handles Fragrance Pack of 100 B0355 / B0355A (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.75,
  },
  {
    id: "10116270424228",
    name: "Pet Dog Compostable Poop Bag Rolls Pack of 120 (6 Rolls) Unscented 323881 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/rsz_0_800_cfe4f_323881_ed891064-44b5-4386-8800-1edc44a5c393.jpg?v=1747569115",
    description:
      "Pet Dog Compostable Poop Bag Rolls Pack of 120 (6 Rolls) Unscented 323881 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.85,
  },
  {
    id: "8310954590372",
    name: "Pet Dog / Cat Backpack Carrier Bag 40 cm Assorted Colours 4981 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/Pet_Bag_Item_Code_4989_6.95_123.jpg?v=1700012375",
    description:
      "Pet Dog / Cat Backpack Carrier Bag 40 cm Assorted Colours 4981 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 8.95,
  },
  {
    id: "8311182786724",
    name: "Plastic Dog Poop Bags 28 x 22 cm Pack of 80 Assorted Colours 0043 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/00430.5010.jpg?v=1700015821",
    description:
      "Plastic Dog Poop Bags 28 x 22 cm Pack of 80 Assorted Colours 0043 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.55,
  },
  {
    id: "8310954688676",
    name: "Pet Dog Cat Puppy Sling Carrier Travel Shoulder Bag Backpack Holder 4300 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/Pet_Travel_Carrier_4300_6.95_1.jpg?v=1700012376",
    description:
      "Pet Dog Cat Puppy Sling Carrier Travel Shoulder Bag Backpack Holder 4300 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 8.95,
  },
  {
    id: "8311142514852",
    name: "Doggy Poop Bags with Tie Handles 30 x 36 cm Roll of 50 B0197 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/B0197.png?v=1741865640",
    description:
      "Doggy Poop Bags with Tie Handles 30 x 36 cm Roll of 50 B0197 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.75,
  },
  {
    id: "8310455369892",
    name: "Cat Litter Tray Bin Bag Liners with Drawstring 70 x 45 cm Roll of 12 B0367 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/products/Capture_03c0d9df-562b-43c9-a804-ce2eaa6add0f.png?v=1749466645",
    description:
      "Cat Litter Tray Bin Bag Liners with Drawstring 70 x 45 cm Roll of 12 B0367 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.75,
  },
  {
    id: "8506527023268",
    name: "Cat Litter Bin Bag Liners with Drawstring 70 x 43 cm Roll of 15 B0015 / 100438 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/100438.png?v=1712678820",
    description:
      "Cat Litter Bin Bag Liners with Drawstring 70 x 43 cm Roll of 15 B0015 / 100438 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.75,
  },
  {
    id: "10116269867172",
    name: "Pet Dog Compostable Poop Bag Rolls Pack of 120 (6 Rolls) Lavender Scented 323882 (Parcel Rate)",
    image:
      "https://www.wholesaledealsdirect.co.uk/cdn/shop/files/rsz_0_800_43f08_323882_541de52e-ce15-48d3-84de-4f89da870ec2.jpg?v=1747139242",
    description:
      "Pet Dog Compostable Poop Bag Rolls Pack of 120 (6 Rolls) Lavender Scented 323882 (Parcel Rate)",
    category: "Pet Supplies",
    date: null,
    blogTitle: null,
    price: 0.85,
  },
];

// Using the same rendering logic as allProducts.js
document.addEventListener("DOMContentLoaded", () => {
  const productList = document.getElementById("product-list");
  const paginationContainer = document.createElement("div");
  paginationContainer.classList.add(
    "pagination-container",
    "d-flex",
    "justify-content-center",
    "mt-4"
  );
  productList.parentElement.appendChild(paginationContainer);

  // Initial settings
  let itemsPerPage = 12;
  let currentPage = 1;
  let sortedProducts = [...petItems];

  const sortSelect = document.getElementById("sortSelect");
  const displaySelect = document.getElementById("displaySelect");

  // ==========================
  // SORTING HANDLER
  // ==========================
  function applySorting() {
    const sortValue = sortSelect?.value;
    sortedProducts = [...petItems]; // reset before sorting

    switch (sortValue) {
      case "priceLowHigh":
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case "priceHighLow":
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case "nameAZ":
        sortedProducts.sort((a, b) =>
          a.name.localeCompare(b.name, undefined, { sensitivity: "base" })
        );
        break;
      case "nameZA":
        sortedProducts.sort((a, b) =>
          b.name.localeCompare(a.name, undefined, { sensitivity: "base" })
        );
        break;
      case "newest":
        sortedProducts.sort(
          (a, b) =>
            new Date(b.date || "1970-01-01") - new Date(a.date || "1970-01-01")
        );
        break;
      default:
        break;
    }

    currentPage = 1;
    renderProducts(currentPage);
    renderPagination();
  }

  // ==========================
  // DISPLAY COUNT HANDLER
  // ==========================
  if (displaySelect) {
    displaySelect.addEventListener("change", () => {
      itemsPerPage = parseInt(displaySelect.value, 10);
      currentPage = 1;
      renderProducts(currentPage);
      renderPagination();
    });
  }

  if (sortSelect) {
    sortSelect.addEventListener("change", applySorting);
  }

  // ==========================
  // RENDER PRODUCTS
  // ==========================
  function renderProducts(page) {
    productList.innerHTML = "";

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex);

    // Determine base path for details page depending on location
    const detailsBase = window.location.pathname.includes("/pages/")
      ? ""
      : "pages/";

    paginatedProducts.forEach((product, index) => {
      const productColumn = document.createElement("div");
      productColumn.classList.add("product-grid-item");
      productColumn.setAttribute("data-aos", "fade-up");
      productColumn.setAttribute("data-aos-delay", `${index * 100}`);
      productColumn.innerHTML = `
        <article class="post-item product-item" 
          data-price="${product.price}" 
          data-product-id="${product.id}">
          <div class="post-image">
            <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
              <img src="${product.image}" alt="${product.name}" 
                   class="post-grid-image img-fluid">
            </a>
          </div>
          <div class="post-content">
            <div class="post-meta text-uppercase fs-6 text-secondary">
              <span class="post-category">${product.category} /</span>
              <span class="meta-day">${product.date ?? ""}</span>
            </div>
            <h5 class="post-title text-uppercase">
              <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
                ${product.name}
              </a>
            </h5>
            <p>${product.description}</p>
            <button class="add-to-cart btn btn-primary btn-sm" 
              data-product-id="${product.id}" 
              data-product-name="${product.name}" 
              data-product-price="${product.price}">
              Add to Cart - £${product.price ? product.price.toFixed(2) : "N/A"}
            </button>
          </div>
        </article>
      `;
      productList.appendChild(productColumn);
    });

    if (typeof AOS !== "undefined") AOS.refresh();

    if (typeof window.reinitializeFilters === "function") {
      setTimeout(() => window.reinitializeFilters(), 100);
    }
  }

  // ==========================
  // PAGINATION HANDLER
  // ==========================
  function renderPagination() {
    const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

    if (totalPages <= 1) {
      paginationContainer.innerHTML = "";
      return;
    }

    let paginationHTML = `
    <nav aria-label="Product navigation">
      <ul class="pagination">
        <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage - 1
          }" aria-label="Previous">
            <span aria-hidden="true">&laquo;</span>
          </a>
        </li>
  `;

    // ✅ Only show 3 pages at a time
    const maxVisiblePages = 3;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
      <li class="page-item ${currentPage === i ? "active" : ""}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
    }

    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage + 1
          }" aria-label="Next">
            <span aria-hidden="true">&raquo;</span>
          </a>
        </li>
      </ul>
    </nav>
  `;

    paginationContainer.innerHTML = paginationHTML;

    // Add page navigation listeners
    const pageLinks = paginationContainer.querySelectorAll(".page-link");
    pageLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const newPage = parseInt(e.target.closest(".page-link").dataset.page);
        if (
          !isNaN(newPage) &&
          newPage !== currentPage &&
          newPage > 0 &&
          newPage <= totalPages
        ) {
          currentPage = newPage;
          renderProducts(currentPage);
          renderPagination();
          productList.scrollIntoView({ behavior: "smooth" });
        }
      });
    });
  }

  // ==========================
  // CACHE PRODUCT ON CLICK
  // ==========================
  document.addEventListener("click", function (e) {
    const link = e.target.closest("a.product-link");
    if (!link) return;

    const item = e.target.closest(".product-item");
    const id = item?.getAttribute("data-product-id");
    if (!id) return;

    const prod = petItems.find((p) => String(p.id) === String(id));
    if (prod) {
      localStorage.setItem("lastClickedProduct", JSON.stringify(prod));
    } else {
      localStorage.setItem("lastClickedProduct", JSON.stringify({ id }));
    }
  });

  // ==========================
  // INITIAL RENDER
  // ==========================
  renderProducts(currentPage);
  renderPagination();
});
