<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment Cancelled - Kaira</title>
    <meta name="description" content="Payment was cancelled">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="apple-touch-icon" href="../apple-touch-icon.png">
    <link rel="stylesheet" href="../css/normalize.css">
    <link rel="stylesheet" href="../css/vendor.css">
    <link rel="stylesheet" href="../css/swiper-bundle.min.css">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <script src="../js/modernizr.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div id="header-placeholder"></div>

    <main>
        <section class="padding-large">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 text-center">
                        <div class="card shadow-sm">
                            <div class="card-body py-5">
                                <div class="mb-4">
                                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" fill="#dc3545"/>
                                        <path d="M15 9l-6 6M9 9l6 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <h2 class="text-danger mb-3">Payment Cancelled</h2>
                                <p class="lead mb-4">Your payment was cancelled. No charges were made to your account.</p>
                                <p class="text-muted mb-4">Your cart items are still available. You can complete your purchase at any time.</p>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="checkout.html" class="btn btn-primary btn-lg me-md-2">Try Again</a>
                                    <a href="../index.html" class="btn btn-outline-secondary btn-lg">Continue Shopping</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="footer" class="bg-light py-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-4 text-center text-lg-start">
                    <p class="mb-0">&copy; 2023 Kaira. All rights reserved.</p>
                </div>
                <div class="col-lg-4 text-center py-3 py-lg-0">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item me-3">
                            <a href="#">
                                <img src="../images/visa-card.png" alt="Visa Card">
                            </a>
                        </li>
                        <li class="list-inline-item me-3">
                            <a href="#">
                                <img src="../images/master-card.png" alt="Master Card">
                            </a>
                        </li>
                        <li class="list-inline-item">
                            <a href="#">
                                <img src="../images/paypal-card.png" alt="Paypal Card">
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 text-center text-lg-end">
                    <p class="mb-0">
                        <a href="#" class="text-dark">Privacy Policy</a>
                        <span class="px-2">|</span>
                        <a href="#" class="text-dark">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/plugins.js"></script>
    <script src="../js/script.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Fetch and inject the header
        fetch('header.html')
            .then(response => response.text())
            .then(html => {
                const placeholder = document.getElementById('header-placeholder');
                placeholder.innerHTML = html;
                
                // Fix navigation links for subpages
                const navLinks = placeholder.querySelectorAll('.navbar-nav a');
                navLinks.forEach(link => {
                    if (link.textContent.trim() === 'Home') {
                        link.setAttribute('href', '../index.html');
                    }
                    
                    const href = link.getAttribute('href');
                    if (href && href.startsWith('pages/')) {
                        link.setAttribute('href', href.replace('pages/', ''));
                    }
                });

                const accountLinks = placeholder.querySelectorAll('.col-lg-auto a');
                accountLinks.forEach(link => {
                    const href = link.getAttribute('href');
                    if (href && href.startsWith('pages/')) {
                        link.setAttribute('href', href.replace('pages/', ''));
                    } else if (href === 'index.html') {
                        link.setAttribute('href', '../index.html');
                    }
                });
            })
            .catch(console.error);
    </script>
</body>
</html>
