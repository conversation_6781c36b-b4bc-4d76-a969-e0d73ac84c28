// Product filtering functionality
function initializeFilters() {
    // Get filter elements
    const searchInput = document.getElementById('searchInput');
    const minPrice = document.getElementById('minPrice');
    const maxPrice = document.getElementById('maxPrice');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const resetFiltersBtn = document.getElementById('resetFilters');
    
    // Find the product container
    const productContainer = document.querySelector('.products-grid') || 
                           document.querySelector('.product-grid') || 
                           document.querySelector('#product-list') || 
                           document.querySelector('.row');

    if (!productContainer) {
        console.error('Product container not found');
        return;
    }

    // Add filter feedback elements
    const filterFeedback = document.createElement('div');
    filterFeedback.className = 'filter-feedback mt-3';
    productContainer.parentNode.insertBefore(filterFeedback, productContainer);

    // Store original products and their data
    let originalProducts = [];
    let productData = new Map();

    // Function to collect and cache all products
    function collectProducts() {
        if (originalProducts.length === 0) {
            const products = productContainer.querySelectorAll('.col-lg-4, .col-md-4, .product-item, [class*="product"]');
            originalProducts = Array.from(products);
            
            // Cache product data for better performance
            originalProducts.forEach(product => {
                const price = getProductPrice(product);
                const text = getProductText(product);
                console.log(`Found product with price: ${price}, text: ${text}`);
                productData.set(product, { price, text });
            });
        }
        return originalProducts;
    }

    // Enhanced price extraction function
    function getProductPrice(product) {
        // Try different selectors to find price
        const priceSelectors = ['.price', '.product-price', '[class*="price"]', 'h6', 'p'];
        
        for (const selector of priceSelectors) {
            const elements = product.querySelectorAll(selector);
            for (const el of elements) {
                const text = el.textContent.trim();
                // Look for currency symbols and numbers
                if (/[$£€]|\d+(\.\d{1,2})?/.test(text)) {
                    // Extract the first valid price found
                    const matches = text.match(/[\d,]+\.?\d*/);
                    if (matches) {
                        // Remove commas and convert to float
                        const price = parseFloat(matches[0].replace(/,/g, ''));
                        if (!isNaN(price)) {
                            console.log(`Found price: ${price} in text: ${text}`);
                            return price;
                        }
                    }
                }
            }
        }
        console.log(`No price found in product:`, product.textContent.substring(0, 100));
        return 0;
    }

    // Enhanced text extraction for search
    function getProductText(product) {
        // Get all text content from relevant elements
        const textSelectors = ['.product-title', '.title', 'h2', 'h3', 'h4', '.product-name', '[class*="title"]'];
        let texts = [];
        
        for (const selector of textSelectors) {
            const elements = product.querySelectorAll(selector);
            elements.forEach(el => texts.push(el.textContent.trim()));
        }
        
        // If no specific elements found, use all text content
        if (texts.length === 0) {
            texts = [product.textContent.trim()];
        }
        
        return texts.join(' ').toLowerCase();
    }

    // Enhanced filtering function
    function filterProducts() {
        const products = collectProducts();
        if (products.length === 0) {
            console.error('No products found to filter');
            return;
        }

        const searchTerm = searchInput.value.toLowerCase().trim();
        const min = parseFloat(minPrice.value) || 0;
        const max = parseFloat(maxPrice.value) || Infinity;

        console.log(`Filtering with: search="${searchTerm}", min=${min}, max=${max}`);

        let visibleCount = 0;
        let priceRange = { min: Infinity, max: -Infinity };

        products.forEach(product => {
            const data = productData.get(product);
            if (!data) return;

            const matchesSearch = !searchTerm || data.text.includes(searchTerm);
            const matchesPrice = data.price >= min && (max === Infinity || data.price <= max);
            
            if (matchesSearch && matchesPrice) {
                product.style.display = '';
                visibleCount++;
                priceRange.min = Math.min(priceRange.min, data.price);
                priceRange.max = Math.max(priceRange.max, data.price);
            } else {
                product.style.display = 'none';
            }
        });

        // Update feedback
        updateFilterFeedback(visibleCount, priceRange, min, max);
    }

    // Function to update filter feedback
    function updateFilterFeedback(count, priceRange, min, max) {
        const actualMin = priceRange.min === Infinity ? 0 : priceRange.min;
        const actualMax = priceRange.max === -Infinity ? 0 : priceRange.max;

        filterFeedback.innerHTML = `
            <div class="alert ${count === 0 ? 'alert-warning' : 'alert-info'}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${count === 0 
                            ? '<strong>No products found</strong> matching your criteria' 
                            : `Showing <strong>${count}</strong> product${count !== 1 ? 's' : ''}`}
                    </div>
                    <div class="text-muted">
                        Price range: $${actualMin.toFixed(2)} - $${actualMax.toFixed(2)}
                    </div>
                </div>
                ${count === 0 ? `
                    <div class="mt-2">
                        <small>
                            Try adjusting your filters:
                            ${min > actualMin ? `<br>• Minimum price (${min}) is too high` : ''}
                            ${max < actualMax ? `<br>• Maximum price (${max}) is too low` : ''}
                            ${searchTerm ? `<br>• Search term might be too specific` : ''}
                        </small>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary mt-2" onclick="document.getElementById('resetFilters').click()">
                        Reset Filters
                    </button>
                ` : ''}
            </div>
        `;
    }

    // Price input validation and formatting
    function setupPriceInputs() {
        [minPrice, maxPrice].forEach(input => {
            input.addEventListener('input', (e) => {
                // Remove non-numeric characters except decimal point
                let value = e.target.value.replace(/[^\d.]/g, '');
                
                // Ensure only one decimal point
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                
                // Limit to 2 decimal places
                if (parts[1] && parts[1].length > 2) {
                    value = parts[0] + '.' + parts[1].slice(0, 2);
                }
                
                e.target.value = value;
            });

            // Auto-filter on change
            input.addEventListener('change', filterProducts);
        });
    }

    // Reset filters function
    function resetFilters() {
        searchInput.value = '';
        minPrice.value = '';
        maxPrice.value = '';
        
        products.forEach(product => {
            product.style.display = '';
        });

        filterFeedback.innerHTML = '';
    }

    // Set up event listeners
    setupPriceInputs();
    applyFiltersBtn.addEventListener('click', filterProducts);
    resetFiltersBtn.addEventListener('click', resetFilters);
    searchInput.addEventListener('input', debounce(filterProducts, 300));

    // Initialize
    collectProducts();
    console.log('Filter initialization complete');
}

// Debounce helper function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize filters when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeFilters);
