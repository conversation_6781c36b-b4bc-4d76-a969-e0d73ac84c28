// User authentication state management
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;

// Function to check if user is logged in
function isLoggedIn() {
  return currentUser !== null;
}

// Function to log in user
function loginUser(user) {
  // Store user info in localStorage
  const userData = {
    displayName: user.displayName || user.email.split('@')[0],
    email: user.email,
    uid: user.uid
  };
  localStorage.setItem('currentUser', JSON.stringify(userData));
  currentUser = userData;
  updateAuthUI();
}

// Function to log out user
function logoutUser() {
  localStorage.removeItem('currentUser');
  currentUser = null;
  updateAuthUI();
}

// Function to update UI based on auth state
function updateAuthUI() {
  // Update desktop auth container
  const authContainer = document.querySelector('.auth-container');
  if (authContainer) {
    updateAuthElement(authContainer, true);
  }

  // Update mobile auth container if exists
  const mobileAuthContainer = document.querySelector('.auth-container-mobile');
  if (mobileAuthContainer) {
    updateMobileAuthElement(mobileAuthContainer);
  }
}

// Helper function to update the desktop auth element
function updateAuthElement(authElement, isDesktop) {
  if (isLoggedIn()) {
    // User is logged in - show name and logout button
    authElement.innerHTML = `
      <span class="user-greeting me-2">Hello, ${currentUser.displayName}</span>
      <button class="btn btn-outline-secondary btn-sm logout-btn">Logout</button>
    `;
    
    // Add event listener to logout button
    const logoutBtn = authElement.querySelector('.logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        // If using Firebase, sign out from Firebase too
        if (window.firebase && firebase.auth) {
          firebase.auth().signOut()
            .then(() => {
              logoutUser();
              // Redirect to home page if on a protected page
              window.location.href = window.location.pathname.includes('/pages/') 
                ? '../index.html' 
                : 'index.html';
            })
            .catch(error => console.error('Logout error:', error));
        } else {
          logoutUser();
          // Redirect to home page if on a protected page
          window.location.href = window.location.pathname.includes('/pages/') 
            ? '../index.html' 
            : 'index.html';
        }
      });
    }
  } else {
    // User is not logged in - show login/register links
    const isInPagesFolder = window.location.pathname.includes('/pages/');
    const loginPath = isInPagesFolder ? 'login.html' : 'pages/login.html';
    const registerPath = isInPagesFolder ? 'register.html' : 'pages/register.html';

    authElement.innerHTML = `
      <a href="${loginPath}" class="me-3">Login</a>
      <a href="${registerPath}">Register</a>
    `;
  }
}

// Helper function to update the mobile auth element
function updateMobileAuthElement(mobileElement) {
  const isInPagesFolder = window.location.pathname.includes('/pages/');
  
  if (isLoggedIn()) {
    // For logged-in users, make the user icon open a dropdown with logout option
    mobileElement.setAttribute('data-bs-toggle', 'dropdown');
    mobileElement.setAttribute('aria-expanded', 'false');
    
    // Remove any existing dropdown menu
    const existingDropdown = mobileElement.nextElementSibling;
    if (existingDropdown && existingDropdown.classList.contains('dropdown-menu')) {
      existingDropdown.remove();
    }
    
    // Create dropdown menu
    const dropdownMenu = document.createElement('ul');
    dropdownMenu.className = 'dropdown-menu';
    dropdownMenu.innerHTML = `
      <li><span class="dropdown-item-text">Hello, ${currentUser.displayName}</span></li>
      <li><hr class="dropdown-divider"></li>
      <li><a class="dropdown-item logout-mobile" href="#">Logout</a></li>
    `;
    
    // Insert dropdown menu after the mobile auth element
    mobileElement.parentNode.insertBefore(dropdownMenu, mobileElement.nextSibling);
    
    // Add event listener to mobile logout option
    const logoutOption = dropdownMenu.querySelector('.logout-mobile');
    logoutOption.addEventListener('click', (e) => {
      e.preventDefault();
      if (window.firebase && firebase.auth) {
        firebase.auth().signOut()
          .then(() => {
            logoutUser();
            window.location.href = isInPagesFolder ? '../index.html' : 'index.html';
          })
          .catch(error => console.error('Logout error:', error));
      } else {
        logoutUser();
        window.location.href = isInPagesFolder ? '../index.html' : 'index.html';
      }
    });
  } else {
    // For logged-out users, make the user icon link to login page
    mobileElement.removeAttribute('data-bs-toggle');
    mobileElement.removeAttribute('aria-expanded');
    mobileElement.href = isInPagesFolder ? 'login.html' : 'pages/login.html';
    
    // Remove any existing dropdown menu
    const existingDropdown = mobileElement.nextElementSibling;
    if (existingDropdown && existingDropdown.classList.contains('dropdown-menu')) {
      existingDropdown.remove();
    }
  }
}

// Initialize auth state on page load
document.addEventListener('DOMContentLoaded', () => {
  // Check if Firebase is available and initialized
  if (window.firebase && firebase.auth) {
    // Listen for auth state changes from Firebase
    firebase.auth().onAuthStateChanged(user => {
      if (user) {
        loginUser(user);
      } else if (currentUser) {
        // User is logged out from Firebase but still in localStorage
        logoutUser();
      }
      updateAuthUI();
    });
  } else {
    // No Firebase, just use localStorage state
    updateAuthUI();
  }
});

// Export functions for use in other files
window.authUtils = {
  isLoggedIn,
  loginUser,
  logoutUser,
  getCurrentUser: () => currentUser,
  updateAuthUI
}; 