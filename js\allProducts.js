document.addEventListener("DOMContentLoaded", () => {
  const productList = document.getElementById("product-list");
  const paginationContainer = document.createElement("div");
  paginationContainer.classList.add(
    "pagination-container",
    "d-flex",
    "justify-content-center",
    "mt-4"
  );
  productList.parentElement.appendChild(paginationContainer);

  // Initial settings
  let itemsPerPage = 12;
  let currentPage = 1;
  let sortedProducts = [...products];

  const sortSelect = document.getElementById("sortSelect");
  const displaySelect = document.getElementById("displaySelect");

  // ==========================
  // SORTING HANDLER
  // ==========================
  function applySorting() {
    const sortValue = sortSelect?.value;
    sortedProducts = [...products]; // reset before sorting

    switch (sortValue) {
      case "priceLowHigh":
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case "priceHighLow":
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case "nameAZ":
        sortedProducts.sort((a, b) =>
          a.name.localeCompare(b.name, undefined, { sensitivity: "base" })
        );
        break;
      case "nameZA":
        sortedProducts.sort((a, b) =>
          b.name.localeCompare(a.name, undefined, { sensitivity: "base" })
        );
        break;
      case "newest":
        sortedProducts.sort(
          (a, b) =>
            new Date(b.date || "1970-01-01") - new Date(a.date || "1970-01-01")
        );
        break;
      default:
        break;
    }

    currentPage = 1;
    renderProducts(currentPage);
    renderPagination();
  }

  // ==========================
  // DISPLAY COUNT HANDLER
  // ==========================
  if (displaySelect) {
    displaySelect.addEventListener("change", () => {
      itemsPerPage = parseInt(displaySelect.value, 10);
      currentPage = 1;
      renderProducts(currentPage);
      renderPagination();
    });
  }

  if (sortSelect) {
    sortSelect.addEventListener("change", applySorting);
  }

  // ==========================
  // RENDER PRODUCTS
  // ==========================
  function renderProducts(page) {
    productList.innerHTML = "";

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex);

    // Determine base path for details page
    const detailsBase = window.location.pathname.includes("/pages/")
      ? ""
      : "pages/";

    paginatedProducts.forEach((product, index) => {
      const productColumn = document.createElement("div");
      productColumn.classList.add("product-grid-item");
      productColumn.setAttribute("data-aos", "fade-up");
      productColumn.setAttribute("data-aos-delay", `${index * 100}`);
      productColumn.innerHTML = `
        <article class="post-item product-item" 
          data-price="${product.price}" 
          data-product-id="${product.id}">
          <div class="post-image">
            <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
              <img src="${product.image}" alt="${product.name}" 
                   class="post-grid-image img-fluid">
            </a>
          </div>
          <div class="post-content">
            <div class="post-meta text-uppercase fs-6 text-secondary">
              <span class="post-category">${product.category} /</span>
              <span class="meta-day">${product.date ?? ""}</span>
            </div>
            <h5 class="post-title text-uppercase">
              <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
                ${product.name}
              </a>
            </h5>
            <p>${product.description}</p>
            <button class="add-to-cart btn btn-primary btn-sm" 
              data-product-id="${product.id}" 
              data-product-name="${product.name}" 
              data-product-price="${product.price}">
              Add to Cart - $${product.price ? product.price.toFixed(2) : "N/A"}
            </button>
          </div>
        </article>
      `;
      productList.appendChild(productColumn);
    });

    // Reinitialize animations
    if (typeof AOS !== "undefined") AOS.refresh();

    // Reinitialize filters if defined elsewhere
    if (typeof window.reinitializeFilters === "function") {
      setTimeout(() => window.reinitializeFilters(), 100);
    }
  }

  // ==========================
  // PAGINATION HANDLER
  // ==========================
  function renderPagination() {
    const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

    if (totalPages <= 1) {
      paginationContainer.innerHTML = "";
      return;
    }

    let paginationHTML = `
    <nav aria-label="Product navigation">
      <ul class="pagination">
        <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage - 1
          }" aria-label="Previous">
            <span aria-hidden="true">&laquo;</span>
          </a>
        </li>
  `;

    // ✅ Only show 3 pages at a time
    const maxVisiblePages = 3;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
      <li class="page-item ${currentPage === i ? "active" : ""}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
    }

    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage + 1
          }" aria-label="Next">
            <span aria-hidden="true">&raquo;</span>
          </a>
        </li>
      </ul>
    </nav>
  `;

    paginationContainer.innerHTML = paginationHTML;

    // Add page navigation listeners
    const pageLinks = paginationContainer.querySelectorAll(".page-link");
    pageLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const newPage = parseInt(e.target.closest(".page-link").dataset.page);
        if (
          !isNaN(newPage) &&
          newPage !== currentPage &&
          newPage > 0 &&
          newPage <= totalPages
        ) {
          currentPage = newPage;
          renderProducts(currentPage);
          renderPagination();
          productList.scrollIntoView({ behavior: "smooth" });
        }
      });
    });
  }

  // ==========================
  // CACHE PRODUCT ON CLICK
  // ==========================
  document.addEventListener("click", function (e) {
    const link = e.target.closest("a.product-link");
    if (!link) return;

    const item = e.target.closest(".product-item");
    const id = item?.getAttribute("data-product-id");
    if (!id) return;

    const prod = products.find((p) => String(p.id) === String(id));
    if (prod) {
      localStorage.setItem("lastClickedProduct", JSON.stringify(prod));
    } else {
      localStorage.setItem("lastClickedProduct", JSON.stringify({ id }));
    }
  });

  // ==========================
  // INITIAL RENDER
  // ==========================
  renderProducts(currentPage);
  renderPagination();
});
