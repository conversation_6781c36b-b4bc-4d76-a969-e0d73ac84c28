/* Product Details Styles */
.product-gallery {
  position: relative;
  margin-bottom: 2rem;
}

.main-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}

#mainProductImage {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.thumbnails-container {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail.active {
  border-color: #007bff;
}

/* Product Info Styles */
.product-info {
  padding: 1rem 0;
}

.product-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.product-price {
  font-size: 1.5rem;
  color: #007bff;
  margin-bottom: 1.5rem;
}

.product-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Product Options */
.size-options,
.color-options {
  margin-bottom: 2rem;
}

.options-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.size-btn,
.color-btn {
  min-width: 40px;
  height: 40px;
  margin-right: 0.5rem;
  border: 1px solid #ddd;
  background: #fff;
  transition: all 0.3s ease;
}

.size-btn {
  padding: 0.5rem 1rem;
}

.size-btn:hover,
.color-btn:hover {
  border-color: #007bff;
}

.size-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.color-btn {
  border-radius: 50%;
  position: relative;
}

.color-btn.active::after {
  content: "";
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #007bff;
  border-radius: 50%;
}

/* Quantity Controls */
.quantity-control {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  background: #fff;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background-color: #f8f9fa;
}

#quantity {
  width: 60px;
  height: 40px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 0;
  margin: 0 0.5rem;
}

/* Action Buttons */
.product-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

#addToCart {
  flex: 2;
}

#addToWishlist {
  flex: 1;
}

/* Related Products */
.related-products {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid #ddd;
}

.related-products-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .product-title {
    font-size: 1.5rem;
  }

  .thumbnail {
    width: 60px;
    height: 60px;
  }

  .product-actions {
    flex-direction: column;
  }

  #addToCart,
  #addToWishlist {
    width: 100%;
  }
}

/* Product Features */
.product-features {
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid #ddd;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.feature-icon {
  width: 24px;
  height: 24px;
  margin-right: 1rem;
  color: #007bff;
}

/* Social Share */
.social-share {
  margin-top: 2rem;
}

.social-share-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: #007bff;
  color: white;
}
