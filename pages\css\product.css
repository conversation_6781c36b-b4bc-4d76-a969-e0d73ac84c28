/* General Body and Container Styles */
body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; /* Modern, clean font */
  margin: 0;
  padding: 0;
  background-color: #ffffff; /* Pure white background as in the image */
  color: #000000; /* Black text */
  line-height: 1.5; /* Standard line height */
}

/* Header Placeholder */
#header-placeholder {
  margin-bottom: 0; /* No margin as in the image, assuming header is full width */
  box-shadow: none; /* No shadow on header placeholder */
}

/* Product Page Wrapper - Layout and Spacing */
.product-page-wrapper {
  display: flex;
  flex-direction: column; /* Default to column for small screens */
  max-width: 1080px; /* Adjusted max width to better fit image */
  margin: 60px auto; /* Centered with more top/bottom margin */
  padding: 0; /* No padding on wrapper itself */
  background-color: #fff;
  border-radius: 0; /* No border-radius for a sharper look */
  box-shadow: none; /* No box shadow for a clean look */
  gap: 0; /* No gap between main sections, handled by internal padding/margins */
}

.logo h2 {
  font: "Marcellus", Roboto, sans-serif;
}
/* Product Image Section */
.product-image-section {
  flex: 0 0 65%; /* Image section takes up 65% width on large screens */
  display: flex; /* Arrange images side-by-side */
  justify-content: flex-start; /* Align images to the start */
  align-items: flex-start; /* Align images to the top */
  padding: 0; /* No padding on the section itself */
  border: none; /* No border */
  border-radius: 0;
  overflow: hidden;
}

.main-product-image {
  flex: 0 0 65%; /* Main image takes larger portion */
  max-width: 65%;
  padding: 0;
  overflow: hidden;
}

.secondary-product-image {
  flex: 0 0 35%; /* Secondary image takes smaller portion */
  max-width: 35%;
  padding: 0;
  overflow: hidden;
}

.main-product-image img,
.secondary-product-image img {
  width: 100%; /* Images fill their container */
  height: auto;
  display: block; /* Remove extra space below image */
  border-radius: 0; /* No border-radius on images */
  transition: none; /* No transition for a static look */
}

/* Product Details Section */
.product-details-section {
  flex: 0 0 35%; /* Details section takes up 35% width on large screens */
  padding: 20px 0 20px 40px; /* Top/bottom padding and left padding for details, matching image */
  border-left: none; /* No border left */
}

.product-details {
  padding: 0; /* No internal padding */
}

.product-details h1 {
  font-size: 2.5rem; /* Larger font size for product name */
  color: #000000; /* Black color for title */
  margin-bottom: 5px; /* Closer to price */
  padding-bottom: 0;
  border-bottom: none; /* No underline for heading */
  font-weight: normal; /* Normal font weight */
}

/* Hide the product description as it's not in the image */
#product-description {
  display: none;
}

.product-details p#product-price {
  font-size: 1.5rem; /* Larger font size for price */
  color: #e63946; /* Specific red for price */
  margin-bottom: 10px; /* Closer to underline */
  font-weight: bold; /* Bold price */
}

/* Price Underline */
.price-underline {
  width: 60px; /* Short line under price */
  height: 2px;
  background-color: #000000; /* Black line */
  margin-bottom: 30px; /* Space below line */
}

/* Product Buttons */
.product-buttons {
  display: flex;
  flex-direction: column; /* Stack buttons vertically for smaller screens */
  gap: 10px; /* Small gap between buttons */
  margin-top: 0;
  margin-bottom: 30px; /* Space below buttons */
}

/* Hide the quantity input as it's not in the image */
.quantity-input {
  display: none;
}

.add-to-cart-btn {
  background-color: #000000; /* Black background */
  color: white;
  border: 1px solid #000000; /* Black border */
  padding: 12px 30px; /* Larger padding */
  font-size: 0.9rem; /* Slightly smaller font for button text */
  font-weight: bold; /* Bold button text */
  border-radius: 0; /* Sharp corners */
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  box-shadow: none; /* No shadow */
  text-transform: uppercase; /* Uppercase text */
  width: 100%; /* Full width for button on smaller screens */
  box-sizing: border-box; /* Include padding and border in the element's total width */
}

.add-to-cart-btn:hover {
  background-color: #333333; /* Slightly lighter black on hover */
  transform: none; /* No transform */
}

.buy-it-now-btn {
  background-color: #ffffff; /* White background */
  color: #000000; /* Black text */
  border: 1px solid #000000; /* Black border */
  padding: 12px 30px; /* Same padding as add to cart */
  font-size: 0.9rem;
  font-weight: bold;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  box-shadow: none;
  text-transform: uppercase;
  width: 100%; /* Full width for button on smaller screens */
  box-sizing: border-box;
}

.buy-it-now-btn:hover {
  background-color: #f0f0f0; /* Light grey on hover */
}

/* Full Detail Link */
.full-detail-link {
  font-size: 1rem;
  margin-bottom: 30px; /* Space below link */
}

.full-detail-link a {
  color: #000000; /* Black link color */
  text-decoration: none; /* No underline by default */
  font-weight: normal; /* Normal font weight */
}

.full-detail-link a:hover {
  text-decoration: underline; /* Underline on hover */
}

/* Social Icons */
.social-icons {
  display: flex;
  gap: 15px; /* Space between icons */
  margin-top: 20px;
}

.social-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px; /* Fixed size for icons */
  height: 24px;
  border: 1px solid #000000; /* Black border for icons */
  border-radius: 0; /* Sharp corners */
  color: #000000;
  text-decoration: none;
  font-size: 1rem;
  font-weight: bold; /* If text is used like the 'F' */
  transition: background-color 0.2s ease; /* Smooth transition for hover */
}

.social-icon:hover {
  background-color: #f0f0f0; /* Light grey background on hover */
}

/* Responsive Design */
@media (min-width: 768px) {
  .product-page-wrapper {
    flex-direction: row; /* Row layout on larger screens */
    padding: 0; /* No padding on wrapper for desktop */
  }

  .product-buttons {
    flex-direction: row; /* Buttons side by side on larger screens */
  }

  .add-to-cart-btn,
  .buy-it-now-btn {
    width: auto; /* Buttons take auto width based on content */
  }
}
