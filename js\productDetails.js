let systemAddToCartRef = null;

document.addEventListener('DOMContentLoaded', function() {
    // Get product ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');

    if (!productId) {
        window.location.href = 'allProducts.html';
        return;
    }

    // Load product details
    loadProductDetails(productId);
    
    // Capture reference to global cart add function before defining local handlers
    if (typeof window.addToCart === 'function') {
        systemAddToCartRef = window.addToCart;
    }

    // Set up event listeners
    setupEventListeners();
    // Header buttons
    const backBtn = document.getElementById('goBackBtn');
    if (backBtn) backBtn.addEventListener('click', () => {
        if (document.referrer && document.referrer !== window.location.href) {
            window.location.href = document.referrer;
        } else if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = 'allProducts.html';
        }
    });
    const viewCartBtn = document.getElementById('viewCartBtn');
    if (viewCartBtn) viewCartBtn.addEventListener('click', () => {
        const offcanvasEl = document.getElementById('offcanvasCart');
        if (offcanvasEl && typeof bootstrap !== 'undefined') {
            new bootstrap.Offcanvas(offcanvasEl).show();
        }
    });
});

function loadProductDetails(productId) {
    // Fetch product details from your data source
    const product = getProductById(productId);
    
    if (!product) {
        // Try reading the last clicked product from localStorage as a fallback
        try {
            const cached = localStorage.getItem('lastClickedProduct');
            if (cached) {
                const parsed = JSON.parse(cached);
                if (String(parsed.id) === String(productId)) {
                    populateFromProduct(parsed);
                    return;
                }
            }
        } catch (e) {}
        window.location.href = 'allProducts.html';
        return;
    }

    populateFromProduct(product);
    
    // Load thumbnails if available
    if (product.images && product.images.length > 0) {
        const thumbnailsContainer = document.getElementById('productThumbnails');
        product.images.forEach((image, index) => {
            const img = document.createElement('img');
            img.src = image;
            img.alt = `${product.name} - View ${index + 1}`;
            img.classList.add('thumbnail');
            if (index === 0) img.classList.add('active');
            img.addEventListener('click', () => updateMainImage(image));
            thumbnailsContainer.appendChild(img);
        });
    }

    // No size/color or related products in simplified view
}

function populateFromProduct(product) {
    document.getElementById('mainProductImage').src = product.image;
    document.getElementById('productTitle').textContent = product.name;
    document.getElementById('productPrice').textContent = `$${Number(product.price).toFixed(2)}`;
    document.getElementById('productDescription').textContent = product.description || '';
}

function setupEventListeners() {
    // Quantity controls
    document.getElementById('decreaseQuantity').addEventListener('click', () => {
        const input = document.getElementById('quantity');
        const value = parseInt(input.value);
        if (value > 1) input.value = value - 1;
    });

    document.getElementById('increaseQuantity').addEventListener('click', () => {
        const input = document.getElementById('quantity');
        input.value = parseInt(input.value) + 1;
    });

    // Add to cart button
    document.getElementById('addToCart').addEventListener('click', detailsAddToCart);
    
    // Add to wishlist button
    document.getElementById('addToWishlist').addEventListener('click', addToWishlist);
}

function updateMainImage(imageSrc) {
    document.getElementById('mainProductImage').src = imageSrc;
    // Update active state of thumbnails
    document.querySelectorAll('.thumbnail').forEach(thumb => {
        thumb.classList.toggle('active', thumb.src === imageSrc);
    });
}

// size/color selection removed in simplified UI

function detailsAddToCart() {
    const productId = getProductIdFromUrl();
    const qty = parseInt(document.getElementById('quantity').value);
    const product = getProductById(productId) || (function(){ try { return JSON.parse(localStorage.getItem('lastClickedProduct')); } catch(e){ return null; } })();
    const addFn = systemAddToCartRef || window.addToCart;
    if (typeof addFn === 'function' && product) {
        for (let i = 0; i < Math.max(1, qty); i++) {
            addFn(String(product.id), product.name || 'Product', product.price || 0);
        }
        try { localStorage.setItem('lastAddedToCart', JSON.stringify({ id: product.id, qty })); } catch (e) {}
        if (typeof renderCart === 'function') renderCart();
        if (typeof renderCheckoutCart === 'function') renderCheckoutCart();
        showAddedPopup();
        return;
    }

    // Fallback: manage cart locally if global function or product is missing
    const fallbackProduct = product || {
        id: productId,
        name: document.getElementById('productTitle')?.textContent || 'Product',
        price: Number((document.getElementById('productPrice')?.textContent || '').replace(/[^0-9.]/g, '')) || 0
    };
    try {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const idx = cart.findIndex(item => String(item.id) === String(fallbackProduct.id));
        if (idx >= 0) {
            cart[idx].quantity += Math.max(1, qty);
        } else {
            cart.push({ id: String(fallbackProduct.id), name: fallbackProduct.name, price: fallbackProduct.price, quantity: Math.max(1, qty) });
        }
        localStorage.setItem('cart', JSON.stringify(cart));
    } catch (e) {}

    if (typeof renderCart === 'function') renderCart();
    if (typeof renderCheckoutCart === 'function') renderCheckoutCart();
    showAddedPopup();
}

function showAddedPopup() {
    if (typeof showCartNotification === 'function') {
        showCartNotification('Product added to cart');
        return;
    }
    // Minimal fallback popup
    alert('Product added to cart');
}

function openCartOffcanvas() {
    const offcanvasEl = document.getElementById('offcanvasCart');
    if (offcanvasEl && typeof bootstrap !== 'undefined') {
        new bootstrap.Offcanvas(offcanvasEl).show();
    }
}

// wishlist removed in simplified UI

// related products removed in simplified UI

function getProductIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
}

// card creation not needed in simplified UI

// Lookup a product by id across available product arrays
function getProductById(productId) {
    const sources = [
        window.products,
        window.backpackProducts,
        window.corkPrintProducts,
        window.starBagProducts,
        window.strapProducts,
        window.toyProducts,
        window.petItemProducts
    ];
    for (const arr of sources) {
        if (Array.isArray(arr)) {
            const match = arr.find(p => String(p.id) === String(productId));
            if (match) return match;
        }
    }
    return null;
}

// Get a few related products by category, excluding the current one
function getRelatedProducts(category) {
    const pool = Array.isArray(window.products) ? window.products : [];
    const currentId = getProductIdFromUrl();
    const related = pool.filter(p => p.category === category && String(p.id) !== String(currentId));
    return related.slice(0, 8);
}

// Delegated handler: clicking a product card navigates to details
document.addEventListener('click', function(e) {
    const link = e.target.closest('.product-item a.product-link, .card a.btn.btn-outline-primary');
    if (!link) return;
    // Let the normal navigation proceed
});
