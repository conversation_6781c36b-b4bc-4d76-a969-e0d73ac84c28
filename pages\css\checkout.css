/* Add your custom styles here */

body {
  background-color: #f8f9fa;
  font-family: "Jo<PERSON>", sans-serif;
  color: #343a40; /* Keep main body text dark for readability */
  line-height: 1.6; /* Slightly increased line height for better readability */
}

.container {
  max-width: 960px;
  padding-left: 15px;
  padding-right: 15px;
}

.lh-sm {
  line-height: 1.25;
}

.checkout-process {
  padding: 70px 0;
  background-color: #ffffff;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.05); /* Slightly more pronounced but soft shadow */
  margin-top: 30px;
  margin-bottom: 30px;
  border-radius: 12px; /* Slightly more rounded corners for a modern look */
}

.card {
  margin-bottom: 1.5rem;
  border: 1px solid #e0e0e0; /* A very light, subtle border for definition */
  border-radius: 0.75rem; /* Slightly more rounded corners for cards */
  transition: all 0.2s ease-in-out; /* Smooth transition for hover effects */
}

.card:hover {
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08); /* Subtle shadow on hover */
}

.card-header {
  background-color: #f8f9fa; /* Light background for header */
  border-bottom: 1px solid #e9ecef;
  padding: 1.2rem 1.5rem; /* Increased padding for headers */
  font-weight: 600;
  font-size: 1.15rem; /* Consistent font size for headers */
  color: #333; /* Darker text for headers */
  border-top-left-radius: 0.75rem; /* Match card border-radius */
  border-top-right-radius: 0.75rem; /* Match card border-radius */
}

.card-header h5 {
  margin-bottom: 0;
  font-weight: 600;
  font-size: 1.25rem; /* Slightly larger heading in card header */
  color: #343a40;
}

.card-body {
  padding: 1.8rem 1.5rem; /* Increased padding for card body */
}

.form-label {
  font-weight: 500;
  color: #555;
  margin-bottom: 0.6rem; /* Increased margin for better separation */
  display: block;
  font-size: 0.92rem; /* Slightly smaller font for labels */
}

.form-control,
.form-select {
  border-radius: 8px; /* More rounded input fields */
  border: 1px solid #c8d0d6; /* Slightly softer border color */
  padding: 0.85rem 1.1rem; /* Slightly more padding for inputs */
  font-size: 0.98rem; /* Slightly larger font size for inputs */
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  height: auto;
  min-height: 44px; /* Slightly taller inputs */
}

.form-control:focus,
.form-select:focus {
  border-color: #8da4b7; /* A more distinct but still soft focus border */
  box-shadow: 0 0 0 0.25rem rgba(141, 164, 183, 0.2); /* Soft focus shadow */
  outline: 0;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.82em; /* Slightly smaller feedback text */
  margin-top: 0.3rem; /* Adjusted margin for feedback */
}

.form-check-input {
  margin-top: 0.25em; /* Fine-tuned vertical alignment */
  border-color: #adb5bd;
  width: 1.1em; /* Slightly larger checkbox/radio size */
  height: 1.1em;
}

.form-check-label {
  font-size: 0.95rem;
  color: #343a40;
  padding-left: 0.4em; /* Small padding to align label text better with input */
}

.input-group-text {
  border-radius: 8px 0 0 8px; /* Match input border-radius */
  background-color: #e9ecef;
  border: 1px solid #c8d0d6; /* Match input border color */
  border-right: none;
  padding: 0.85rem 1.1rem; /* Match input padding */
}

.btn-dark {
  background-color: #2c3e50; /* A slightly softer dark blue for buttons */
  border-color: #2c3e50;
  color: #fff;
  padding: 0.9rem 2rem; /* More generous padding for buttons */
  font-size: 1.05rem; /* Slightly larger font for buttons */
  border-radius: 50rem; /* Pill shape */
  transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out,
    box-shadow 0.3s ease-in-out;
}

.btn-dark:hover {
  background-color: #34495e; /* Slightly darker on hover */
  border-color: #34495e;
  box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.15); /* More pronounced hover shadow */
}

.list-group-item {
  padding: 1.1rem 1.4rem; /* More padding for list items */
  border-color: #f0f0f0; /* Lighter border for list items */
  font-size: 0.98rem; /* Consistent font size */
}

.list-group-item:first-child {
  border-top-left-radius: 0.75rem; /* Match card border-radius */
  border-top-right-radius: 0.75rem; /* Match card border-radius */
}

.list-group-item:last-child {
  border-bottom-left-radius: 0.75rem; /* Match card border-radius */
  border-bottom-right-radius: 0.75rem; /* Match card border-radius */
}

.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}

.text-muted {
  color: #6c757d !important;
}

.text-success {
  color: #28a745 !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.mb-3 {
  margin-bottom: 1.2rem !important; /* Adjusted bottom margin for form groups */
}

.mb-4 {
  margin-bottom: 1.8rem !important; /* Consistent card bottom margin */
}

.py-4 {
  padding-top: 1.2rem !important; /* Slightly more padding for cart summary section */
  padding-bottom: 1.2rem !important;
}

.m-0 {
  margin: 0 !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.col-md-8,
.col-md-4 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.row {
  margin-bottom: 1.8rem; /* Increased row margin for more breathing room */
}

/* Adjustments for better form alignment within columns */
.row.mb-3 > div,
.row.gy-3 > div {
  padding-right: 0.75rem; /* Increased padding */
  padding-left: 0.75rem; /* Increased padding */
}

/* Specific alignment for radio buttons */
.form-check {
  display: flex;
  align-items: flex-start;
  min-height: 1.2rem; /* Increased min-height */
  margin-bottom: 0.7rem; /* Increased space between checks */
}

.form-check .form-check-input {
  flex-shrink: 0;
  margin-top: 0.15em; /* Fine-tuned vertical alignment */
  margin-right: 0.6em; /* More space for input */
}

.form-check .form-check-label {
  margin-bottom: 0;
  padding-top: 0.1em; /* Fine-tuned alignment */
  font-size: 0.98rem; /* Consistent font size */
}

/* Horizontal rule styling */
hr.my-4 {
  border-top: 1px solid #e9ecef;
  margin-top: 2rem !important; /* More margin for hr */
  margin-bottom: 2rem !important;
}

h2,
h4 {
  font-family: "Marcellus", serif;
  color: #343a40;
  line-height: 1.3; /* Adjusted line height for headings */
}
