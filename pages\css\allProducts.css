/* Universal box-sizing for consistent layout */
* {
  box-sizing: border-box;
}

body {
  font-family: "Jo<PERSON>", sans-serif; /* Use Jo<PERSON> as the primary font */
  margin: 0;
  padding: 0;
  background-color: #f8f8f8;
  line-height: 1.6;
  color: #333;
}

.all-products-section {
  padding: 80px 0;
  background-color: #fff;
}

.all-products-section .container {
  max-width: 1200px; /* Set a specific max-width for the container */
  margin: 0 auto; /* Center the container */
  padding: 0 15px; /* Slightly reduced padding to prevent edge touching */
}

.all-products-section
  .d-flex.flex-wrap.justify-content-between.align-items-center {
  margin-top: 0 !important;
  margin-bottom: 50px !important;
}

.all-products-section h1.text-uppercase {
  font-family: "Marcellus", serif; /* Use Marcellus for the heading */
  font-size: 2.5em; /* Match the size from blog posts heading */
  color: #222;
  margin: 0; /* Remove default margin */
  text-align: left; /* Align to left */
  line-height: 1.2;
}

.all-products-section .btn-link {
  font-family: "Jo<PERSON>", sans-serif;
  font-size: 1em;
  color: #555;
  text-decoration: none;
  text-transform: uppercase;
  border-bottom: 1px solid #555;
  padding-bottom: 2px;
  transition: color 0.3s ease, border-color 0.3s ease;
}

.all-products-section .btn-link:hover {
  color: #000;
  border-color: #000;
}

#product-list {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(250px, 1fr)
  ); /* Responsive grid with auto-fit columns */
  row-gap: 65px; /* Slightly smaller gap for tighter layouts */
  column-gap: 20px;
  padding: 0;
}

.breadcrumb-bar {
  background-color: #f8f8f8;
  padding: 12px 15px;
  border-radius: 4px;
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-content {
  font-size: 1.1rem;
  color: #333;
}
.product-heading {
  font-size: 1.1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2pt;
}

.breadcrumb-content i:hover {
  color: #e74c3c;
}

.breadcrumb-content .separator {
  color: #999;
  font-weight: 500;
}

.breadcrumb-title {
  color: #e74c3c;
  font-size: 1rem;
  font-weight: bold;
  text-transform: uppercase;
  margin: 0;
}

.sort-section select {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.9rem;
}

.sort-section label {
  color: #333;
  font-size: 0.95rem;
}

/* Add a media query for very small screens */
@media (max-width: 600px) {
  #product-list {
    grid-template-columns: 1fr; /* Single column on small screens */
    gap: 15px;
  }
}

.post-item {
  text-align: left;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  transition: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: unset; /* Let the grid control min-width */
  width: 100%; /* Ensure post-item takes full available width */
}

.post-image {
  margin-bottom: 20px; /* Increased margin for better spacing */
  max-width: 100%; /* Ensure the image container doesn't exceed parent width */
}

.post-image img {
  width: 100%;
  height: 250px; /* Adjusted height to be more rectangular */
  object-fit: cover;
  display: block;
}

.post-content {
  padding: 0;
  width: 100%; /* Ensure post-content takes full available width */
  flex-grow: 1; /* Allow content to grow and fill available space */
}

.post-meta {
  font-family: "Jost", sans-serif;
  font-size: 0.8em;
  color: #777;
  margin-bottom: 8px; /* Slightly increased spacing below meta */
  text-transform: uppercase;
  letter-spacing: 0.1em; /* Add letter spacing for uppercase */
  font-weight: 500; /* Ensure consistent font-weight */
}

.post-meta .post-category {
  font-weight: 500;
}

.post-title {
  font-family: "Marcellus", serif; /* Use Marcellus for product names/titles */
  font-size: 1.4em; /* Slightly larger title */
  font-weight: 400; /* Change to normal weight */
  color: #222;
  margin-bottom: 0px; /* Remove bottom margin for tighter spacing */
  line-height: 1.3;
  text-transform: uppercase;
  letter-spacing: 0.05em; /* Add subtle letter spacing to title */
  white-space: nowrap; /* Prevent title from wrapping */
  overflow: hidden; /* Hide overflow content */
  text-overflow: ellipsis; /* Add ellipsis for truncated text */
}

.post-title a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.post-title a:hover {
  color: #007bff;
}

.post-content p {
  height: 120px;
  font-family: "Jost", sans-serif;
  font-size: 0.85em; /* Slightly smaller font size for description */
  color: #555;
  line-height: 1.6;
  margin-bottom: 0; /* No margin below description */
  white-space: normal; /* Allow text to wrap */
  overflow: hidden; /* Hide overflow content */
  text-overflow: ellipsis; /* Add ellipsis for truncated text */
}

.product-price {
  /* display: none; */ /* Hide product price */
}

.add-to-cart {
  margin-top: auto; /* Add top margin for spacing */
  margin-bottom: 0px; /* Remove bottom margin to align with image */
}

/* Pagination Styles */
.pagination-container {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.pagination {
  --bs-pagination-color: var(--bs-dark);
  --bs-pagination-hover-color: var(--bs-dark);
  --bs-pagination-focus-color: var(--bs-dark);
  --bs-pagination-active-bg: var(--bs-dark);
  --bs-pagination-active-border-color: var(--bs-dark);
}

.pagination .page-link {
  border: none;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  font-size: 1rem;
  border-radius: 0;
}

.pagination .page-item.active .page-link {
  background-color: var(--bs-dark);
  color: var(--bs-light);
}

.pagination .page-item.disabled .page-link {
  opacity: 0.5;
  pointer-events: none;
}
