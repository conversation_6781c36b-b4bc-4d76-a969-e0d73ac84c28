// Create modal HTML structure when the script loads
let modalInitialized = false;

function createModal() {
    if (modalInitialized) return;
    
    const modalHTML = `
        <div id="productModal" class="product-modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <div class="modal-body">
                    <h2 id="modalProductName"></h2>
                    <img id="modalProductImage" src="" alt="">
                    <p id="modalProductDescription"></p>
                    <p id="modalProductPrice"></p>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listener to close button
    const modal = document.getElementById('productModal');
    const closeBtn = modal.querySelector('.close-modal');
    
    closeBtn.onclick = function() {
        modal.style.display = "none";
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    }

    // Close modal when pressing Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = "none";
        }
    });

    modalInitialized = true;
}

// Function to show product details in modal
function showProductDetails(product) {
    console.log('Showing product details:', product); // Debug log
    const modal = document.getElementById('productModal');
    if (!modal) {
        console.error('Modal not found!');
        return;
    }

    const modalName = document.getElementById('modalProductName');
    const modalImage = document.getElementById('modalProductImage');
    const modalDescription = document.getElementById('modalProductDescription');
    const modalPrice = document.getElementById('modalProductPrice');

    modalName.textContent = product.name;
    modalImage.src = product.image;
    modalImage.alt = product.name;
    modalDescription.textContent = product.description;
    modalPrice.textContent = `Price: $${product.price.toFixed(2)}`;

    modal.style.display = "block";
}

// Function to handle product clicks
function handleProductClick(event) {
    // Find the closest product card (supports either .product-item or .post-item)
    const productItem = event.target.closest('.product-item, .post-item');
    if (!productItem) return;

    let productId = productItem.getAttribute('data-product-id');
    if (!productId) {
        const idEl = productItem.querySelector('[data-product-id]');
        if (idEl) productId = idEl.getAttribute('data-product-id');
    }
    if (!productId) {
        console.error('No product ID found on clicked element');
        return;
    }

    console.log('Clicked product ID:', productId); // Debug log

    // First try to find the product in the current page's array
    let product = null;
    const currentProducts = window[getCurrentPage().toLowerCase() + 'Products'] || window.products;
    if (currentProducts) {
        product = currentProducts.find(p => p.id === productId);
    }

    // If not found, search in all product arrays
    if (!product) {
        const allArrays = [
            window.products,
            window.backpackProducts,
            window.corkPrintProducts,
            window.starBagProducts,
            window.strapProducts,
            window.toyProducts,
            window.petItemProducts
        ];

        for (const arr of allArrays) {
            if (arr) {
                product = arr.find(p => p.id === productId);
                if (product) break;
            }
        }
    }

    // Cache and navigate to details page instead of modal
    if (product) {
        try { localStorage.setItem('lastClickedProduct', JSON.stringify(product)); } catch (e) {}
    }
    const detailsBase = window.location.pathname.includes('/pages/') ? '' : 'pages/';
    window.location.href = `${detailsBase}productDetails.html?id=${productId}`;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    createModal();
    
    // Add a single event listener on the document for all product clicks
    document.addEventListener('click', function(event) {
        // Allow normal navigation when clicking explicit product detail links
        const link = event.target.closest('a.product-link');
        if (link) {
            // Cache minimal product data for fallback on details page
            const item = event.target.closest('.product-item, .post-item');
            const productId = item?.getAttribute('data-product-id');
            if (productId) {
                let product = null;
                // Prefer a provided finder if present
                if (typeof findProductById === 'function') {
                    try { product = findProductById(productId); } catch (_) {}
                }
                // Try common globals
                if (!product) {
                    const arrays = [
                        window.products,
                        window.backpackProducts,
                        window.corkPrintProducts,
                        window.starBagProducts,
                        window.strapProducts,
                        window.toyProducts,
                        window.petItemProducts
                    ];
                    // Also attempt page-scoped collections
                    try { if (typeof products !== 'undefined') arrays.push(products); } catch (_) {}
                    try { if (typeof petItems !== 'undefined') arrays.push(petItems); } catch (_) {}
                    try { if (typeof toys !== 'undefined') arrays.push(toys); } catch (_) {}

                    for (const arr of arrays) {
                        if (Array.isArray(arr)) {
                            const match = arr.find(p => String(p.id) === String(productId));
                            if (match) { product = match; break; }
                        }
                    }
                }
                if (product) {
                    try { localStorage.setItem('lastClickedProduct', JSON.stringify(product)); } catch (e) {}
                } else {
                    // At least persist the id
                    try { localStorage.setItem('lastClickedProduct', JSON.stringify({ id: productId })); } catch (e) {}
                }
            }
            return;
        }
        if (event.target.closest('.product-item, .post-item')) {
            handleProductClick(event);
        }
    });
});
