/* === LOGIN PAGE STYLE (House of Milano Inspired) === */

body {
  background-color: #fff !important;
  font-family: 'Jo<PERSON>', sans-serif;
  color: #000;
}

.logo-text {
  font-family: '<PERSON><PERSON>', serif;
  letter-spacing: 1px;
  color: #000;
}

.login-section {
  background-color: #fff;
}

h3, h4 {
  font-family: 'Marcellus', serif;
  color: #000;
  letter-spacing: 0.5px;
}

.container {
  max-width: 1000px;
}

.border {
  border: 1px solid #e5e5e5 !important;
}

.form-label {
  font-weight: 500;
  font-size: 14px;
  color: #000;
}

.form-control {
  border-radius: 0 !important;
  border: 1px solid #ddd !important;
  padding: 10px;
  font-size: 14px;
}

.form-control:focus {
  box-shadow: none !important;
  border-color: #d62828 !important;
}

.form-check-label {
  font-size: 13px;
}

.btn-danger {
  background-color: #d62828;
  border: none;
  border-radius: 0 !important;
  padding: 10px;
  font-size: 14px;
  letter-spacing: 1px;
  transition: background-color 0.3s ease;
}

.btn-danger:hover {
  background-color: #b21d1d;
}

.btn-outline-secondary {
  color: #555;
  border-color: #ddd;
}

.btn-outline-secondary:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.text-muted {
  font-size: 14px;
  color: #777 !important;
}

a.text-muted:hover {
  color: #d62828 !important;
}

input::placeholder {
  color: #999;
  font-size: 13px;
}

footer {
  background-color: #fff;
  color: #777;
  border-top: 1px solid #eee;
}
