// Add styles for filter messages
const filterStyles = document.createElement('style');
filterStyles.textContent = `
    .filter-results-message {
        padding: 10px 15px;
        border-radius: 4px;
        margin: 10px 0;
        transition: all 0.3s ease;
    }
    .filter-results-message.alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
    }
    .filter-results-message.alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
`;
document.head.appendChild(filterStyles);

// Global filter state
let filterState = {
    initialized: false,
    elements: null,
    container: null,
    products: [],
    originalDisplay: new Map()
};

// Product filtering functionality
function initializeFilters() {
    // Initialize filter elements
    const searchInput = document.getElementById('searchInput');
    const minPrice = document.getElementById('minPrice');
    const maxPrice = document.getElementById('maxPrice');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const resetFiltersBtn = document.getElementById('resetFilters');

    // Find product container with multiple selectors
    const productContainer = findProductContainer();

    if (!productContainer) {
        console.error('Product container not found');
        return;
    }

    // Store elements in global state
    filterState.elements = {
        searchInput,
        minPrice,
        maxPrice,
        applyFiltersBtn,
        resetFiltersBtn
    };
    filterState.container = productContainer;

    // Find product container function
    function findProductContainer() {
        const selectors = [
            '.products-grid',
            '.product-grid',
            '#product-list',
            '.row:not(.filter-row):not(.header-row)'
        ];

        for (const selector of selectors) {
            const container = document.querySelector(selector);
            if (container) return container;
        }
        return null;
    }

    // Initialize products
    function initializeProducts() {
        if (filterState.initialized) return filterState.products;

        const productSelectors = [
            '.col-lg-4', 
            '.col-md-4', 
            '.product-item', 
            '[class*="product"]'
        ];

        const products = [];
        productSelectors.forEach(selector => {
            const elements = productContainer.querySelectorAll(selector);
            elements.forEach(el => {
                if (!products.includes(el)) {
                    products.push(el);
                    filterState.originalDisplay.set(el, el.style.display);
                }
            });
        });

        filterState.products = products;
        filterState.initialized = true;
        console.log(`Found ${products.length} products`);
        return products;
    }

    // Extract price from element
    function extractPrice(element) {
        // First try to find price in data attribute if it exists
        const dataPrice = element.getAttribute('data-price');
        if (dataPrice) {
            return parseFloat(dataPrice) || 0;
        }

        // Look for price in the add-to-cart button which has data-product-price
        const addToCartBtn = element.querySelector('.add-to-cart');
        if (addToCartBtn) {
            const btnPrice = addToCartBtn.getAttribute('data-product-price');
            if (btnPrice) {
                return parseFloat(btnPrice) || 0;
            }
        }

        // Look for price in specific elements first
        const priceElements = element.querySelectorAll('.price, .product-price, [class*="price"]');
        for (const el of priceElements) {
            const match = el.textContent.match(/[\$£€]?\s*(\d+(?:\.\d{2})?)/);
            if (match) {
                return parseFloat(match[1]) || 0;
            }
        }

        // Fallback to searching in all text content for price patterns
        const priceMatch = element.textContent.match(/[\$£€]?\s*(\d+(?:\.\d{2})?)/);
        if (priceMatch) {
            return parseFloat(priceMatch[1]) || 0;
        }

        return 0;
    }

    // Extract searchable text from element
    function extractText(element) {
        return element.textContent.trim().toLowerCase();
    }

    // Filter products function
    function filterProducts() {
        const products = initializeProducts();
        if (products.length === 0) return;

        // Show loading state
        productContainer.classList.add('loading');

        // Get filter values
        const searchTerm = searchInput.value.toLowerCase().trim();
        const min = parseFloat(minPrice.value);
        const max = parseFloat(maxPrice.value);

        let visibleCount = 0;
        
        // Apply filters
        products.forEach(product => {
            const price = extractPrice(product);
            const text = extractText(product);
            
            // Debug price extraction
            console.log('Product:', text, 'Price:', price);
            
            const matchesSearch = !searchTerm || text.includes(searchTerm);
            const matchesPrice = (isNaN(min) || price >= min) && (isNaN(max) || price <= max);
            
            if (matchesSearch && matchesPrice) {
                product.style.display = filterState.originalDisplay.get(product) || '';
                visibleCount++;
            } else {
                product.style.display = 'none';
            }
        });

        // Update UI
        updateFilterResults(visibleCount);
        
        // Remove loading state
        setTimeout(() => {
            productContainer.classList.remove('loading');
        }, 300);
    }

    // Update filter results UI
    function updateFilterResults(count) {
        // Remove existing message
        const existing = document.querySelector('.filter-results-message');
        if (existing) existing.remove();

        // Create new message
        const message = document.createElement('div');
        message.className = 'filter-results-message alert ' + 
                          (count === 0 ? 'alert-warning' : 'alert-info');
        
        if (count === 0) {
            message.innerHTML = `
                <h5>No products found</h5>
                <p>Try adjusting your search criteria or price range</p>
                <button class="btn btn-outline-secondary btn-sm" onclick="document.getElementById('resetFilters').click()">
                    Reset Filters
                </button>
            `;
        } else {
            message.textContent = `Showing ${count} product${count === 1 ? '' : 's'}`;
        }

        // Insert message
        productContainer.parentNode.insertBefore(message, productContainer);
    }

    // Reset filters function
    function resetFilters() {
        searchInput.value = '';
        minPrice.value = '';
        maxPrice.value = '';
        
        filterState.products.forEach(product => {
            product.style.display = filterState.originalDisplay.get(product) || '';
        });

        const message = document.querySelector('.filter-results-message');
        if (message) message.remove();
    }

    // Function to reinitialize products (called when new products are loaded)
    function reinitializeProducts() {
        filterState.initialized = false;
        filterState.products = [];
        filterState.originalDisplay.clear();
        initializeProducts();
    }

    // Event listeners
    let debounceTimer;
    
    // Search input with debouncing
    searchInput.addEventListener('input', () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(filterProducts, 300);
    });

    // Price input validation
    [minPrice, maxPrice].forEach(input => {
        input.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^\d.]/g, '');
            const parts = e.target.value.split('.');
            if (parts.length > 2) {
                e.target.value = parts[0] + '.' + parts.slice(1).join('');
            }
        });
        
        input.addEventListener('change', filterProducts);
    });

    // Button listeners
    applyFiltersBtn.addEventListener('click', filterProducts);
    resetFiltersBtn.addEventListener('click', resetFilters);

    // Initialize
    initializeProducts();
}

// Global function to reinitialize filters (can be called from other scripts)
window.reinitializeFilters = function() {
    if (filterState.elements && filterState.container) {
        // Reset the initialized state so products are re-scanned
        filterState.initialized = false;
        filterState.products = [];
        filterState.originalDisplay.clear();
        
        // Reinitialize products
        initializeFilters();
    }
};

// Initialize filters when DOM is loaded and after dynamic content is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initial initialization
    initializeFilters();
    
    // Re-initialize after a short delay to catch dynamically loaded content
    setTimeout(initializeFilters, 1000);
    
    // Re-initialize after longer delay for slower loading
    setTimeout(initializeFilters, 3000);

    // Watch for changes in the DOM that might indicate new products being loaded
    const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            if (mutation.addedNodes.length > 0) {
                // Check if any added nodes contain product-related elements
                const hasProducts = Array.from(mutation.addedNodes).some(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        return node.querySelector && (
                            node.querySelector('.product-grid-item') ||
                            node.querySelector('.post-item') ||
                            node.querySelector('.add-to-cart')
                        );
                    }
                    return false;
                });
                
                if (hasProducts) {
                    // Re-initialize filters when new products are added
                    setTimeout(initializeFilters, 100);
                }
            }
        }
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
});
