.product-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    animation: modalOpen 0.3s ease-out;
}

@keyframes modalOpen {
    from {
        transform: translateY(-10%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close-modal {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.close-modal:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
    text-align: center;
}

.modal-body img {
    max-width: 400px;
    width: 100%;
    height: auto;
    margin: 20px auto;
    display: block;
    border-radius: 4px;
}

.modal-body h2 {
    margin-bottom: 15px;
    color: #333;
    font-size: 24px;
}

.modal-body p {
    margin: 10px 0;
    color: #666;
    line-height: 1.6;
}

#modalProductPrice {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    margin-top: 20px;
}

/* Make product items clickable */
.product-item {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
