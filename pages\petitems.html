<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pet Items</title>

    <!-- Bootstrap CSS first -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css"
    />
    <!-- Vendor and custom styles after Bootstrap -->
    <link rel="stylesheet" href="../css/vendor.css" />
    <link rel="stylesheet" href="../style.css" />
    <!-- allProducts.css (existing) -->
    <link rel="stylesheet" href="css/allProducts.css" />
    <link rel="stylesheet" href="css/filters.css" />
    <!-- AOS CSS -->
    <link rel="stylesheet" href="../css/vendor.css" />
  </head>
  <body>
    <!-- Header placeholder -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display: none">
      <defs>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="instagram"
          viewBox="0 0 15 15"
        >
          <path
            fill="none"
            stroke="currentColor"
            d="M11 3.5h1M4.5.5h6a4 4 0 0 1 4 4v6a4 4 0 0 1-4 4h-6a4 4 0 0 1-4-4v-6a4 4 0 0 1 4-4Zm3 10a3 3 0 1 1 0-6a3 3 0 0 1 0 6Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="facebook"
          viewBox="0 0 15 15"
        >
          <path
            fill="none"
            stroke="currentColor"
            d="M7.5 14.5a7 7 0 1 1 0-14a7 7 0 0 1 0 14Zm0 0v-8a2 2 0 0 1 2-2h.5m-5 4h5"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="twitter"
          viewBox="0 0 15 15"
        >
          <path
            fill="currentColor"
            d="m14.478 1.5l.5-.033a.5.5 0 0 0-.871-.301l.371.334Zm-.498 2.959a.5.5 0 1 0-1 0h1Zm-6.49.082h-.5h.5Zm0 .959h.5h-.5Zm-6.99 7V12a.5.5 0 0 0-.278.916L.5 12.5Zm.998-11l.469-.175a.5.5 0 0 0-.916-.048l.447.223Zm3.994 9l.354.353a.5.5 0 0 0-.195-.827l-.159.474Zm7.224-8.027l-.37.336l.18.199l.265-.04l-.075-.495Zm1.264-.94c.051.778.003 1.25-.123 1.606c-.122.345-.336.629-.723 1l.692.722c.438-.42.776-.832.974-1.388c.193-.546.232-1.178.177-2.006l-.998.066Zm0 3.654V4.46h-1v.728h1Zm-6.99-.646V5.5h1v-.959h-1Zm0 .959V6h1v-.5h-1ZM10.525 1a3.539 3.539 0 0 0-3.537 3.541h1A2.539 2.539 0 0 1 10.526 2V1Zm2.454 4.187C12.98 9.503 9.487 13 5.18 13v1c4.86 0 8.8-3.946 8.8-8.813h-1ZM1.03 1.675C1.574 3.127 3.614 6 7.49 6V5C4.174 5 2.421 2.54 1.966 1.325l-.937.35Zm.021-.398C.004 3.373-.157 5.407.604 7.139c.759 1.727 2.392 3.055 4.73 3.835l.317-.948c-2.155-.72-3.518-1.892-4.132-3.29c-.612-1.393-.523-3.11.427-5.013l-.895-.446Zm4.087 8.87C4.536 10.75 2.726 12 .5 12v1c2.566 0 4.617-1.416 5.346-2.147l-.708-.706Zm7.949-8.009A3.445 3.445 0 0 0 10.526 1v1c.721 0 1.37.311 1.82.809l.74-.671Zm-.296.83a3.513 3.513 0 0 0 2.06-1.134l-.744-.668a2.514 2.514 0 0 1-1.466.813l.15.989ZM.222 12.916C1.863 14.01 3.583 14 5.18 14v-1c-1.63 0-3.048-.011-4.402-.916l-.556.832Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="youtube"
          viewBox="0 0 15 15"
        >
          <path
            fill="currentColor"
            d="m1.61 12.738l-.104.489l.105-.489Zm11.78 0l.104.489l-.105-.489Zm0-10.476l.104-.489l-.105.489Zm-11.78 0l.106.489l-.105-.489ZM6.5 5.5l.277-.416A.5.5 0 0 0 6 5.5h.5Zm0 4H6a.5.5 0 0 0 .777.416L6.5 9.5Zm3-2l.277.416a.5.5 0 0 0 0-.832L9.5 7.5ZM0 3.636v7.728h1V3.636H0Zm15 7.728V3.636h-1v7.728h1ZM1.506 13.227c3.951.847 8.037.847 11.988 0l-.21-.978a27.605 27.605 0 0 1-11.568 0l-.21.978ZM13.494 1.773a28.606 28.606 0 0 0-11.988 0l.21.978a27.607 27.607 0 0 1 11.568 0l.21-.978ZM15 3.636c0-.898-.628-1.675-1.506-1.863l-.21.978c.418.09.716.458.716.885h1Zm-1 7.728a.905.905 0 0 1-.716.885l.21.978A1.905 1.905 0 0 0 15 11.364h-1Zm-14 0c0 .898.628 1.675 1.506 1.863l.21-.978A.905.905 0 0 1 1 11.364H0Zm1-7.728c0-.427.298-.796.716-.885l-.21-.978A1.905 1.905 0 0 0 0 3.636h1ZM6 5.5v4h1v-4H6Zm.777 4.416l3-2l-.554-.832l-3 2l.554.832Zm3-2.832l-3-2l-.554.832l3 2l.554-.832Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="dribble"
          viewBox="0 0 15 15"
        >
          <path
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M4.839 1.024c3.346 4.041 5.096 7.922 5.704 12.782M.533 6.82c5.985-.138 9.402-1.083 11.97-4.216M2.7 12.594c3.221-4.902 7.171-5.65 11.755-4.293M14.5 7.5a7 7 0 1 0-14 0a7 7 0 0 0 14 0Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="calendar"
          viewBox="0 0 24 24"
        >
          <g
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          >
            <rect width="20" height="18" x="2" y="4" rx="4" />
            <path d="M8 2v4m8-4v4M2 10h20" />
          </g>
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="shopping-bag"
          viewBox="0 0 24 24"
        >
          <g
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          >
            <path
              d="M3.977 9.84A2 2 0 0 1 5.971 8h12.058a2 2 0 0 1 1.994 1.84l.803 10A2 2 0 0 1 18.833 22H5.167a2 2 0 0 1-1.993-2.16l.803-10Z"
            />
            <path d="M16 11V6a4 4 0 0 0-4-4v0a4 4 0 0 0-4 4v5" />
          </g>
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="gift"
          viewBox="0 0 24 24"
        >
          <g
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          >
            <rect width="18" height="14" x="3" y="8" rx="2" />
            <path d="M12 5a3 3 0 1 0-3 3m6 0a3 3 0 1 0-3-3m0 0v17m9-7H3" />
          </g>
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="arrow-cycle"
          viewBox="0 0 24 24"
        >
          <g
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          >
            <path
              d="M22 12c0 6-4.39 10-9.806 10C7.792 22 4.24 19.665 3 16m-1-4C2 6 6.39 2 11.806 2C16.209 2 19.76 4.335 21 8"
            />
            <path d="m7 17l-4-1l-1 4M17 7l4 1l1-4" />
          </g>
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="link"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M12 19a1 1 0 1 0-1-1a1 1 0 0 0 1 1Zm5 0a1 1 0 1 0-1-1a1 1 0 0 0 1 1Zm0-4a1 1 0 1 0-1-1a1 1 0 0 0 1 1Zm-5 0a1 1 0 1 0-1-1a1 1 0 0 0 1 1Zm7-12h-1V2a1 1 0 0 0-2 0v1H8V2a1 1 0 0 0-2 0v1H5a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3Zm1 17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-9h16Zm0-11H4V6a1 1 0 0 1 1-1h1v1a1 1 0 0 0 2 0V5h8v1a1 1 0 0 0 2 0V5h1a1 1 0 0 1 1 1ZM7 15a1 1 0 1 0-1-1a1 1 0 0 0 1 1Zm0 4a1 1 0 1 0-1-1a1 1 0 0 0 1 1Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="arrow-left"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M17 11H9.41l3.3-3.29a1 1 0 1 0-1.42-1.42l-5 5a1 1 0 0 0-.21.33a1 1 0 0 0 0 .76a1 1 0 0 0 .21.33l5 5a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42L9.41 13H17a1 1 0 0 0 0-2Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="arrow-right"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M17.92 11.62a1 1 0 0 0-.21-.33l-5-5a1 1 0 0 0-1.42 1.42l3.3 3.29H7a1 1 0 0 0 0 2h7.59l-3.3 3.29a1 1 0 0 0 0 1.42a1 1 0 0 0 1.42 0l5-5a1 1 0 0 0 .21-.33a1 1 0 0 0 0-.76Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="play"
          viewBox="0 0 24 24"
        >
          <g fill="none" fill-rule="evenodd">
            <path
              d="M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"
            />
            <path
              fill="currentColor"
              d="M5.669 4.76a1.469 1.469 0 0 1 2.04-1.177c1.062.454 3.442 1.533 6.462 3.276c3.021 1.744 5.146 3.267 6.069 3.958c.788.591.79 1.763.001 2.356c-.914.687-3.013 2.19-6.07 3.956c-3.06 1.766-5.412 2.832-6.464 3.28c-.906.387-1.92-.2-2.038-1.177c-.138-1.142-.396-3.735-.396-7.237c0-3.5.257-6.092.396-7.235Z"
            />
          </g>
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="category"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M19 5.5h-6.28l-.32-1a3 3 0 0 0-2.84-2H5a3 3 0 0 0-3 3v13a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-10a3 3 0 0 0-3-3Zm1 13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-13a1 1 0 0 1 1-1h4.56a1 1 0 0 1 .95.68l.54 1.64a1 1 0 0 0 .95.68h7a1 1 0 0 1 1 1Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="calendar"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M19 4h-2V3a1 1 0 0 0-2 0v1H9V3a1 1 0 0 0-2 0v1H5a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3Zm1 15a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-7h16Zm0-9H4V7a1 1 0 0 1 1-1h2v1a1 1 0 0 0 2 0V6h6v1a1 1 0 0 0 2 0V6h2a1 1 0 0 1 1 1Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="heart"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M20.16 4.61A6.27 6.27 0 0 0 12 4a6.27 6.27 0 0 0-8.16 9.48l7.45 7.45a1 1 0 0 0 1.42 0l7.45-7.45a6.27 6.27 0 0 0 0-8.87Zm-1.41 7.46L12 18.81l-6.75-6.74a4.28 4.28 0 0 1 3-7.3a4.25 4.25 0 0 1 3 1.25a1 1 0 0 0 1.42 0a4.27 4.27 0 0 1 6 6.05Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="plus"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="minus"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M19 11H5a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="cart"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M8.5 19a1.5 1.5 0 1 0 1.5 1.5A1.5 1.5 0 0 0 8.5 19ZM19 16H7a1 1 0 0 1 0-2h8.491a3.013 3.013 0 0 0 2.885-2.176l1.585-5.55A1 1 0 0 0 19 5H6.74a3.007 3.007 0 0 0-2.82-2H3a1 1 0 0 0 0 2h.921a1.005 1.005 0 0 1 .962.725l.155.545v.005l1.641 5.742A3 3 0 0 0 7 18h12a1 1 0 0 0 0-2Zm-1.326-9l-1.22 4.274a1.005 1.005 0 0 1-.963.726H8.754l-.255-.892L7.326 7ZM16.5 19a1.5 1.5 0 1 0 1.5 1.5a1.5 1.5 0 0 0-1.5-1.5Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="check"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M18.71 7.21a1 1 0 0 0-1.42 0l-7.45 7.46l-3.13-3.14A1 1 0 1 0 5.29 13l3.84 3.84a1 1 0 0 0 1.42 0l8.16-8.16a1 1 0 0 0 0-1.47Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="trash"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M10 18a1 1 0 0 0 1-1v-6a1 1 0 0 0-2 0v6a1 1 0 0 0 1 1ZM20 6h-4V5a3 3 0 0 0-3-3h-2a3 3 0 0 0-3 3v1H4a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2ZM10 5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v1h-4Zm7 14a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V8h10Zm-3-1a1 1 0 0 0 1-1v-6a1 1 0 0 0-2 0v6a1 1 0 0 0 1 1Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="star-outline"
          viewBox="0 0 15 15"
        >
          <path
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M7.5 9.804L5.337 11l.413-2.533L4 6.674l2.418-.37L7.5 4l1.082 2.304l2.418.37l-1.75 1.793L9.663 11L7.5 9.804Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="star-solid"
          viewBox="0 0 15 15"
        >
          <path
            fill="currentColor"
            d="M7.953 3.788a.5.5 0 0 0-.906 0L6.08 5.85l-2.154.33a.5.5 0 0 0-.283.843l1.574 1.613l-.373 2.284a.5.5 0 0 0 .736.518l1.92-1.063l1.921 1.063a.5.5 0 0 0 .736-.519l-.373-2.283l1.574-1.613a.5.5 0 0 0-.283-.844L8.921 5.85l-.968-2.062Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="search"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39ZM11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="user"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M15.71 12.71a6 6 0 1 0-7.42 0a10 10 0 0 0-6.22 8.18a1 1 0 0 0 2 .22a8 8 0 0 1 15.9 0a1 1 0 0 0 1 .89h.11a1 1 0 0 0 .88-1.1a10 10 0 0 0-6.25-8.19ZM12 12a4 4 0 1 1 4-4a4 4 0 0 1-4 4Z"
          />
        </symbol>
        <symbol
          xmlns="http://www.w3.org/2000/svg"
          id="close"
          viewBox="0 0 15 15"
        >
          <path
            fill="currentColor"
            d="M7.953 3.788a.5.5 0 0 0-.906 0L6.08 5.85l-2.154.33a.5.5 0 0 0-.283.843l1.574 1.613l-.373 2.284a.5.5 0 0 0 .736.518l1.92-1.063l1.921 1.063a.5.5 0 0 0 .736-.519l-.373-2.283l1.574-1.613a.5.5 0 0 0-.283-.844L8.921 5.85l-.968-2.062Z"
          />
        </symbol>
      </defs>
    </svg>

    <!-- <div class="preloader text-white fs-6 text-uppercase overflow-hidden"></div> -->
    <!-- Top Bar -->
    <div class="top-bar py-2">
      <div class="container-fluid">
        <div class="row justify-content-between align-items-center">
          <div class="col-auto">
            <select class="language-select form-select form-select-sm">
              <option selected>English</option>
              <!-- <option>Spanish</option>
              <option>French</option> -->
            </select>
          </div>
          <div class="col-auto">
            <div class="d-flex gap-3 right-menu">
              <a href="#account">
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-person"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664z"
                  />
                </svg>
                My Account
              </a>
              <a><strong>|</strong></a>
              <a href="#wishlist">
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-heart"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="m8 2.748-.717-.737C5.6.281 2.514.878 1.4 3.053c-.523 1.023-.641 2.5.314 4.385.92 1.815 2.834 3.989 6.286 6.357 3.452-2.368 5.365-4.542 6.286-6.357.955-1.886.838-3.362.314-4.385C13.486.878 10.4.28 8.717 2.01zM8 15C-7.333 4.868 3.279-3.04 7.824 1.143c.06.055.119.112.176.171a3.12 3.12 0 0 1 .176-.17C12.72-3.042 23.333 4.867 8 15"
                  />
                </svg>
                Wishlist (0)
              </a>
              <a><strong>|</strong></a>
              <a onclick="logoutUser()" href="./login.html">
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-unlock"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M11 1a2 2 0 0 0-2 2v4a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h5V3a3 3 0 0 1 6 0v4a.5.5 0 0 1-1 0V3a2 2 0 0 0-2-2M3 8a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1z"
                  />
                </svg>
                Log Out
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Navbar -->
    <div class="main-navbar py-3">
      <div class="container-fluid">
        <div class="row justify-content-between align-items-center g-3">
          <div class="col-lg-4 col-md-6">
            <div class="input-group search-bar">
              <input
                type="text"
                class="form-control"
                placeholder="Search store"
              />
              <button class="btn btn-outline-secondary" type="button">
                <svg
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-search"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div class="col-lg-2 col-md-4">
            <div class="logo mt-4"><h2>ATS</h2></div>
          </div>

          <div class="col-lg-3 col-md-5">
            <div class="cart-info d-flex align-items-center gap-2">
              <a
                href="index.html"
                class="text-uppercase mx-3"
                data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasCart"
                aria-controls="offcanvasCart"
              >
                <svg
                  width="23"
                  height="23"
                  fill="currentColor"
                  class="bi bi-bag"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1m3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1z"
                  />
                </svg>
                <span class="pt-1">
                  Shopping Cart <span class="cart-count">(0)</span></span
                ></a
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="navbar navbar-expand-lg bottom-nav">
      <div class="container-fluid">
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav">
            <li class="nav-item">
              <a class="nav-link" href="../index.html">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="allProducts.html">Handbags</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="BackPacks.html">Backpacks</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="StarBags.html">Star Bags</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="CorkPrint.html">Cork Print</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="straps.html">Strap</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="toys.html">Toys</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="petitems.html">Pet Items</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Offcanvas Cart -->
    <div
      class="offcanvas offcanvas-end"
      data-bs-scroll="true"
      tabindex="-1"
      id="offcanvasCart"
      aria-labelledby="My Cart"
    >
      <div class="offcanvas-header justify-content-center">
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body">
        <div class="order-md-last">
          <h4 class="d-flex justify-content-between align-items-center mb-3">
            <span class="text-primary">Your cart</span>
            <span class="badge bg-primary rounded-pill cart-count">(0)</span>
          </h4>
          <ul class="list-group mb-3">
            <!-- Cart items will be rendered here by cart.js -->
          </ul>
          <button
            class="w-100 btn btn-primary btn-lg"
            onclick="window.location.href='checkout.html'"
          >
            Continue to Checkout
          </button>
        </div>
      </div>
    </div>

    <!-- Main product content -->
    <section class="all-products-section py-5">
      <div class="container">
        <div class="row">
          <!-- Sidebar -->
          <div class="col-md-3">
            <div class="filter-sidebar">
              <h5 class="sidebar-title">Filters</h5>

              <div class="filter-group">
                <label for="searchInput" class="form-label"
                  >Search Products</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="searchInput"
                  placeholder="Search..."
                />
              </div>

              <div class="filter-group">
                <label class="form-label">Price Range</label>
                <div class="d-flex gap-2">
                  <input
                    type="number"
                    class="form-control"
                    id="minPrice"
                    placeholder="Min"
                  />
                  <input
                    type="number"
                    class="form-control"
                    id="maxPrice"
                    placeholder="Max"
                  />
                </div>
              </div>

              <div class="filter-group d-flex gap-2">
                <button class="btn btn-primary w-100" id="applyFilters">
                  Apply
                </button>
                <button
                  class="btn btn-outline-secondary w-100"
                  id="resetFilters"
                >
                  Reset
                </button>
              </div>

              <!-- Categories Section -->
              <div class="categories-section">
                <div class="categories-header">Categories</div>
                <ul class="categories-list">
                  <li>
                    <a href="./allProducts.html">
                      <i class="bi bi-chevron-right"></i> Hand Bags
                    </a>
                  </li>
                  <li>
                    <a href="./BackPacks.html">
                      <i class="bi bi-chevron-right"></i> Back Packs
                    </a>
                  </li>
                  <li>
                    <a href="./StarBags.html">
                      <i class="bi bi-chevron-right"></i> Star Bags
                    </a>
                  </li>
                  <li>
                    <a href="./CorkPrint.html">
                      <i class="bi bi-chevron-right"></i> Cork Print
                    </a>
                  </li>
                  <li>
                    <a href="./straps.html">
                      <i class="bi bi-chevron-right"></i> Strap
                    </a>
                  </li>
                  <li>
                    <a href="./toys.html">
                      <i class="bi bi-chevron-right"></i> Toys
                    </a>
                  </li>
                  <li class="active">
                    <a href="./petitems.html">
                      <i class="bi bi-chevron-right"></i> Pet Items
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Products Grid -->
          <div class="col-md-9">
            <div class="row breadcrumb-bar align-items-center mb-4">
              <div class="col-12">
                <div class="breadcrumb-content">
                  <a href="../index.html"
                    ><i class="bi bi-house-door-fill"></i
                  ></a>
                  <span class="separator">/</span>

                  <h1 class="breadcrumb-title">Pet Items</h1>
                </div>
              </div>
            </div>

            <div class="justify-content-between align-items-center mb-4">
              <h1 class="product-heading mb-4"><strong>Pet Items</strong></h1>
              <div
                class="justify-content-between align-items-center mb-4 d-flex"
              >
                <!-- Sort By Section -->
                <div class="sort-section d-flex align-items-center gap-2">
                  <label for="sortSelect" class="mb-0 fw-semibold"
                    >Sort by:</label
                  >
                  <select
                    id="sortSelect"
                    class="form-select form-select-sm"
                    style="width: 180px"
                  >
                    <option value="default">Default</option>
                    <option value="priceLowHigh">Price: Low to High</option>
                    <option value="priceHighLow">Price: High to Low</option>
                    <option value="nameAZ">Name: A to Z</option>
                    <option value="nameZA">Name: Z to A</option>
                    <option value="newest">Newest Arrivals</option>
                  </select>
                </div>
                <!-- Display Count Section -->
                <div class="display-section d-flex align-items-center gap-2">
                  <label for="displaySelect" class="mb-0 fw-semibold"
                    >Display:</label
                  >
                  <select
                    id="displaySelect"
                    class="form-select form-select-sm"
                    style="width: 100px"
                  >
                    <option value="12">12</option>
                    <option value="48">48</option>
                    <option value="64">64</option>
                  </select>
                </div>
              </div>
            </div>
            <div id="product-list" class="products-grid"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer placeholder -->
    <!-- <div id="footer-placeholder"></div> -->

    <!-- Footer -->
    <div class="newsletter-section">
      <div class="container">
        <div class="newsletter-wrapper">
          <span class="newsletter-text">Newsletter</span>
          <form class="newsletter-form">
            <input
              type="email"
              class="newsletter-input"
              placeholder="Enter Your Email Address"
              required
            />
            <button type="submit" class="newsletter-btn">Subscribe</button>
          </form>
          <div class="social-icons">
            <a href="#" class="social-icon"
              ><i class="fa-brands fa-facebook"></i
            ></a>
            <a href="#" class="social-icon"
              ><i class="fa-brands fa-twitter"></i
            ></a>
            <a href="#" class="social-icon"
              ><i class="fa-brands fa-google"></i
            ></a>
            <a href="#" class="social-icon"
              ><i class="fa-brands fa-youtube"></i
            ></a>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Section -->
    <footer id="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-column">
            <div class="footer-menu footer-menu-002">
              <h5 class="widget-title">Quick Links</h5>
              <ul class="menu-list">
                <li class="menu-item">
                  <a href="../index.html" class="item-anchor">Home</a>
                </li>
                <li class="menu-item">
                  <a href="../index.html" class="item-anchor">About</a>
                </li>
                <li class="menu-item">
                  <a href="../blog.html" class="item-anchor">Services</a>
                </li>
                <li class="menu-item">
                  <a href="../styles.html" class="item-anchor">Single item</a>
                </li>
                <li class="menu-item">
                  <a href="#" class="item-anchor">Contact</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="footer-column">
            <div class="footer-menu footer-menu-003">
              <h5 class="widget-title">Help & Info</h5>
              <ul class="menu-list">
                <li class="menu-item">
                  <a href="#" class="item-anchor">Track Your Order</a>
                </li>
                <li class="menu-item">
                  <a href="#" class="item-anchor">Returns + Exchanges</a>
                </li>
                <li class="menu-item">
                  <a href="#" class="item-anchor">Shipping + Delivery</a>
                </li>
                <li class="menu-item">
                  <a href="#" class="item-anchor">Contact Us</a>
                </li>
                <li class="menu-item">
                  <a href="#" class="item-anchor">Find us easy</a>
                </li>
                <li class="menu-item">
                  <a href="index.html" class="item-anchor">Faqs</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="footer-column">
            <div class="footer-menu footer-menu-004">
              <h5 class="widget-title">Contact Us</h5>
              <p>
                Do you have any questions or suggestions?
                <a href="mailto:<EMAIL>" class="item-anchor"
                  ><EMAIL></a
                >
              </p>
              <p>
                Do you need support? Give us a call.
                <a href="tel:+43 720 11 52 78" class="item-anchor"
                  >+43 720 11 52 78</a
                >
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="footer-bottom">
        <div class="container">
          <div class="bottom-content">
            <div class="footer-info">
              <span
                >We ship with
                <a href="#"
                  ><img src="../images/arct-icon.png" alt="icon" />
                  <img src="../images/dhl-logo.png" alt="icon" /></a
              ></span>
              <span>Copyright © 2025 Shop with ATS. All rights reserved.</span>
            </div>
            <div class="payment-icons">
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/0/04/Visa.svg"
                alt="Visa"
                class="payment-icon"
              />
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
                alt="Mastercard"
                class="payment-icon"
              />
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg"
                alt="Amex"
                class="payment-icon"
              />
            </div>
            <div class="support-info">
              <span>Designed by <a href="#">TemplatesJungle</a></span> |
              <span>Supported by <a href="#">ThemeWagon</a></span>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Load JavaScript in correct order -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/cart.js"></script>
    <script src="../js/petitems.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/newProductFilters.js"></script>
    <!-- Plugins JS for AOS -->
    <script src="../js/plugins.js"></script>
    <!-- Make sure AOS is loaded -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

    <script>
      // Fetch and inject the header
      fetch("header.html")
        .then((response) => response.text())
        .then((html) => {
          const placeholder = document.getElementById("header-placeholder");
          placeholder.innerHTML = html;

          // Fix navigation links for subpages
          const navLinks = placeholder.querySelectorAll(".navbar-nav a");
          navLinks.forEach((link) => {
            // Update the Home link to point to root
            if (link.textContent.trim() === "Home") {
              link.setAttribute("href", "../index.html");
            }

            // Remove "pages/" prefix from links if they have it
            const href = link.getAttribute("href");
            if (href && href.startsWith("pages/")) {
              link.setAttribute("href", href.replace("pages/", ""));
            }
          });

          // Fix user account links
          const accountLinks = placeholder.querySelectorAll(".col-lg-auto a");
          accountLinks.forEach((link) => {
            const href = link.getAttribute("href");
            if (href && href.startsWith("pages/")) {
              link.setAttribute("href", href.replace("pages/", ""));
            } else if (href === "index.html") {
              link.setAttribute("href", "../index.html");
            }
          });

          // Re-initialize Bootstrap components if needed
          if (typeof bootstrap !== "undefined") {
            const offcanvasEl = document.getElementById("offcanvasCart");
            if (offcanvasEl) new bootstrap.Offcanvas(offcanvasEl);
          }
          // Re-initialize cart logic if needed
          if (typeof initCart === "function") initCart();
        })
        .catch(console.error);

      // Fetch and inject the footer
      fetch("footer.html")
        .then((response) => response.text())
        .then((html) => {
          const placeholder = document.getElementById("footer-placeholder");
          placeholder.innerHTML = html;
        })
        .catch(console.error);

      // Initialize AOS
      AOS.init({
        duration: 800,
        once: true,
      });
    </script>
  </body>
</html>
