<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - ATS Store</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Marcellus&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" type="text/css" href="../css/vendor.css">
  <link rel="stylesheet" type="text/css" href="../style.css">
  <link rel="stylesheet" href="css/register.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body>
  <!-- Header placeholder -->
  <div id="header-placeholder"></div>

  <!-- Cart Offcanvas -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasCart" aria-labelledby="offcanvasCartLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasCartLabel">Shopping Cart</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <p>Your cart is empty</p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content py-4">
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
          <!-- Categories -->
          <div class="sidebar-section">
            <h5 class="sidebar-title">Categories</h5>
            <ul class="sidebar-list">
              <li><a href="#"><i class="fas fa-circle"></i> New in</a></li>
              <li><a href="allProducts.html"><i class="fas fa-circle"></i> HANDBAGS</a></li>
              <li><a href="BackPacks.html"><i class="fas fa-circle"></i> Backpacks</a></li>
              <li><a href="StarBags.html"><i class="fas fa-circle"></i> STAR BAGS</a></li>
              <li><a href="CorkPrint.html"><i class="fas fa-circle"></i> CORK PRINT</a></li>
              <li><a href="straps.html"><i class="fas fa-circle"></i> STRAP</a></li>
              <li><a href="toys.html"><i class="fas fa-circle"></i> ACCESSORIES</a></li>
              <li><a href="#"><i class="fas fa-circle"></i> SALE</a></li>
            </ul>
          </div>

          <!-- Popular Tags -->
          <div class="sidebar-section">
            <h5 class="sidebar-title">Popular tags</h5>
            <div class="tag-cloud">
              <span class="tag">camera</span>
              <span class="tag">camera bag</span>
              <span class="tag">camera bags</span>
              <span class="tag">hessian</span>
              <span class="tag">sling</span>
              <span class="tag">sling bag</span>
              <span class="tag">sling bags</span>
              <span class="tag">straps</span>
              <span class="tag">straps camera bags</span>
              <span class="tag">woven bags</span>
            </div>
          </div>
        </div>

        <!-- Registration Form -->
        <div class="col-md-9">
          <div class="register-form-container">
            <h2 class="register-title">REGISTER</h2>

            <form id="registration-form">
              <!-- Personal Details -->
              <div class="form-section">
                <h4 class="section-title">YOUR PERSONAL DETAILS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Gender</label>
                    <div class="gender-options">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="gender" id="male" value="male">
                        <label class="form-check-label" for="male">Male</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="gender" id="female" value="female">
                        <label class="form-check-label" for="female">Female</label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="first-name" class="form-label">First name <span class="required">*</span></label>
                    <input type="text" class="form-control" id="first-name" required>
                  </div>
                  <div class="col-md-6">
                    <label for="last-name" class="form-label">Last name <span class="required">*</span></label>
                    <input type="text" class="form-control" id="last-name" required>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="dob" class="form-label">Date of birth</label>
                    <div class="row">
                      <div class="col-4">
                        <select class="form-select" id="dob-day">
                          <option value="">Day</option>
                          <!-- Days 1-31 will be populated by JavaScript -->
                        </select>
                      </div>
                      <div class="col-4">
                        <select class="form-select" id="dob-month">
                          <option value="">Month</option>
                          <!-- Months will be populated by JavaScript -->
                        </select>
                      </div>
                      <div class="col-4">
                        <select class="form-select" id="dob-year">
                          <option value="">Year</option>
                          <!-- Years will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="email" class="form-label">Email <span class="required">*</span></label>
                    <input type="email" class="form-control email-field" id="email" placeholder="<EMAIL>" required>
                  </div>
                </div>
              </div>

              <!-- Company Details -->
              <div class="form-section">
                <h4 class="section-title">COMPANY DETAILS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="company-name" class="form-label">Company name</label>
                    <input type="text" class="form-control" id="company-name">
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="vat-number" class="form-label">VAT number</label>
                    <input type="text" class="form-control" id="vat-number">
                    <small class="form-text text-muted">NOTE: Enter VAT number with country code e.g. GB ***********</small>
                  </div>
                </div>
              </div>

              <!-- Address Section -->
              <div class="form-section">
                <h4 class="section-title">YOUR ADDRESS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="street-address" class="form-label">Street address</label>
                    <input type="text" class="form-control" id="street-address">
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="zip-code" class="form-label">Zip/postal code</label>
                    <input type="text" class="form-control" id="zip-code">
                  </div>
                  <div class="col-md-6">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" id="city">
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="form-section">
                <h4 class="section-title">YOUR CONTACT INFORMATION</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="phone" class="form-label">Phone <span class="required">*</span></label>
                    <input type="tel" class="form-control" id="phone" required>
                  </div>
                </div>
              </div>

              <!-- Options -->
              <div class="form-section">
                <h4 class="section-title">OPTIONS</h4>

                <div class="form-check mb-3">
                  <input class="form-check-input" type="checkbox" id="newsletter" checked>
                  <label class="form-check-label" for="newsletter">
                    Newsletter
                  </label>
                </div>
              </div>

              <!-- Password Section -->
              <div class="form-section">
                <h4 class="section-title">YOUR PASSWORD</h4>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="password" class="form-label">Password <span class="required">*</span></label>
                    <input type="password" class="form-control password-field" id="password" placeholder="••••••••" required>
                  </div>
                  <div class="col-md-6">
                    <label for="confirm-password" class="form-label">Confirm password <span class="required">*</span></label>
                    <input type="password" class="form-control" id="confirm-password" required>
                  </div>
                </div>
              </div>

              <!-- reCAPTCHA -->
              <div class="form-section">
                <div class="recaptcha-container mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="recaptcha" required>
                    <label class="form-check-label" for="recaptcha">
                      I'm not a robot
                    </label>
                  </div>
                  <div class="recaptcha-logo">
                    <small class="text-muted">reCAPTCHA</small>
                    <small class="text-muted">Privacy - Terms</small>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="form-section">
                <button type="submit" class="btn btn-danger register-btn">REGISTER</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Newsletter Section -->
  <section class="newsletter-section py-5">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="newsletter-content d-flex align-items-center justify-content-between">
            <div class="newsletter-text">
              <h4 class="text-uppercase mb-0">NEWSLETTER</h4>
              <span class="newsletter-subtitle">Enter Your Email Address</span>
            </div>
            <div class="newsletter-form d-flex">
              <input type="email" class="form-control newsletter-input" placeholder="Enter Your Email Address" aria-label="Email">
              <button class="btn newsletter-btn text-uppercase" type="button">SUBSCRIBE</button>
            </div>
            <div class="social-links">
              <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"><i class="fab fa-google-plus-g"></i></a>
              <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
              <a href="#" class="social-link"><i class="fas fa-rss"></i></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark text-light py-5">
    <div class="container">
      <div class="row">
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">MY ACCOUNT</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">My Account</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">CUSTOMER SERVICE</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">News</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">INFORMATION</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Terms & Conditions</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">OUR OFFERS</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Search</a></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Script to fetch and inject header -->
  <script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Fetch and inject the header
      fetch('header.html')
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.text();
        })
        .then(html => {
          const placeholder = document.getElementById('header-placeholder');
          if (placeholder) {
            placeholder.innerHTML = html;

            // Update authentication link based on user state
            const authLink = placeholder.querySelector('.auth-link');
            const authText = placeholder.querySelector('.auth-text');

            if (window.authUtils && window.authUtils.isLoggedIn()) {
              // User is logged in - show logout
              if (authLink) {
                authLink.setAttribute('onclick', 'logoutUser()');
                authLink.setAttribute('href', '#');
              }
              if (authText) {
                authText.textContent = 'Log Out';
              }
            } else {
              // User is not logged in - show login
              if (authLink) {
                authLink.setAttribute('href', 'login.html');
                authLink.removeAttribute('onclick');
              }
              if (authText) {
                authText.textContent = 'Log In';
              }
            }

            // Fix navigation links for subpages
            const navLinks = placeholder.querySelectorAll('.navbar-nav a, .right-menu a');
            navLinks.forEach(link => {
              const href = link.getAttribute('href');
              // Fix relative paths for subpages
              if (href && !href.startsWith('http') && !href.startsWith('../') && !href.startsWith('#')) {
                if (href === 'index.html') {
                  link.setAttribute('href', '../index.html');
                }
                // Other page links stay as they are since we're in pages/ folder
              }
            });

            // Re-initialize Bootstrap components
            if (typeof bootstrap !== 'undefined') {
              // Initialize offcanvas components
              const offcanvasElements = placeholder.querySelectorAll('.offcanvas');
              offcanvasElements.forEach(el => {
                new bootstrap.Offcanvas(el);
              });
            }

            // Ensure all elements are visible
            const topBar = placeholder.querySelector('.top-bar');
            const mainNavbar = placeholder.querySelector('.main-navbar');
            const bottomNav = placeholder.querySelector('.bottom-nav');
            const logo = placeholder.querySelector('.logo');

            if (topBar) topBar.style.display = 'block';
            if (mainNavbar) mainNavbar.style.display = 'block';
            if (bottomNav) bottomNav.style.display = 'block';
            if (logo) logo.style.display = 'block';

            console.log('Header loaded successfully');
            console.log('Top bar found:', !!topBar);
            console.log('Main navbar found:', !!mainNavbar);
            console.log('Bottom nav found:', !!bottomNav);
            console.log('Logo found:', !!logo);
          }
        })
        .catch(error => {
          console.error('Error loading header:', error);
          // Fallback: show a simple navigation
          const placeholder = document.getElementById('header-placeholder');
          if (placeholder) {
            placeholder.innerHTML = `
              <nav class="navbar navbar-expand-lg bg-light">
                <div class="container-fluid">
                  <a class="navbar-brand" href="../index.html">ATS Store</a>
                  <div class="navbar-nav">
                    <a class="nav-link" href="../index.html">Home</a>
                    <a class="nav-link" href="login.html">Login</a>
                    <a class="nav-link" href="register.html">Register</a>
                  </div>
                </div>
              </nav>
            `;
          }
        });
    });
  </script>

  <script>
    // Initialize Firebase with your config
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Populate date dropdowns
    function populateDateDropdowns() {
      const daySelect = document.getElementById('dob-day');
      const monthSelect = document.getElementById('dob-month');
      const yearSelect = document.getElementById('dob-year');

      // Populate days
      for (let i = 1; i <= 31; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        daySelect.appendChild(option);
      }

      // Populate months
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];
      months.forEach((month, index) => {
        const option = document.createElement('option');
        option.value = index + 1;
        option.textContent = month;
        monthSelect.appendChild(option);
      });

      // Populate years (from current year down to 100 years ago)
      const currentYear = new Date().getFullYear();
      for (let i = currentYear; i >= currentYear - 100; i--) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        yearSelect.appendChild(option);
      }
    }

    const registrationForm = document.getElementById('registration-form');

    registrationForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Form submitted'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;

      // Validate password confirmation
      if (password !== confirmPassword) {
        alert('Passwords do not match!');
        return;
      }

      // Check reCAPTCHA
      if (!document.getElementById('recaptcha').checked) {
        alert('Please verify that you are not a robot.');
        return;
      }

      console.log('Attempting to create user with email:', email); // Debug log

      try {
        // Create user with email and password
        const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;

        // Update user profile with full name
        await user.updateProfile({
          displayName: `${firstName} ${lastName}`
        });

        console.log('User created successfully:', user); // Debug log

        // Use our auth util to handle the login
        if (window.authUtils) {
          window.authUtils.loginUser(user);
        }

        alert('Registration successful!');
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Registration error:', error); // Debug log
        alert('Registration error: ' + error.message);
      }
    });

    // Check if user is already logged in
    window.addEventListener('DOMContentLoaded', () => {
      populateDateDropdowns();

      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });
  </script>

</body>
</html>
