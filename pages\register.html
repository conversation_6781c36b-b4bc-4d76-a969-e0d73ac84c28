<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - ATS Store</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Marcellus&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" type="text/css" href="../css/vendor.css">
  <link rel="stylesheet" type="text/css" href="../style.css">
  <link rel="stylesheet" href="css/register.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body>
  <!-- Top Bar -->
  <div class="top-bar py-2">
    <div class="container-fluid">
      <div class="row justify-content-between align-items-center">
        <div class="col-auto">
          <select class="language-select form-select form-select-sm">
            <option selected>English</option>
          </select>
        </div>
        <div class="col-auto">
          <div class="d-flex gap-3 right-menu">
            <a href="register.html">
              <svg width="16" height="16" fill="currentColor" class="bi bi-person-plus" viewBox="0 0 16 16">
                <path d="M6 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664z"/>
                <path fill-rule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5"/>
              </svg>
              Register
            </a>
            <a><strong>|</strong></a>
            <a href="login.html">
              <svg width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0z"/>
                <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708z"/>
              </svg>
              Log In
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Navbar -->
  <div class="main-navbar py-3">
    <div class="container-fluid">
      <div class="row justify-content-between align-items-center g-3">
        <div class="col-lg-4 col-md-6">
          <div class="input-group search-bar">
            <input type="text" class="form-control" placeholder="Search store">
            <button class="btn btn-outline-secondary" type="button">
              <svg width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="col-lg-2 col-md-4">
          <div class="logo text-center">
            <a href="../index.html" class="text-decoration-none">
              <h2>ATS</h2>
            </a>
          </div>
        </div>

        <div class="col-lg-3 col-md-5">
          <div class="cart-info d-flex align-items-center gap-2 justify-content-end">
            <a href="../index.html" class="text-uppercase mx-3">
              <svg width="23" height="23" fill="currentColor" class="bi bi-bag" viewBox="0 0 16 16">
                <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1m3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1z"/>
              </svg>
              <span class="pt-1">Shopping Cart <span class="cart-count">(0)</span></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Navigation -->
  <nav class="navbar navbar-expand-lg bottom-nav">
    <div class="container-fluid">
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="../index.html">HOME</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">NEW IN</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="allProducts.html">HANDBAGS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="BackPacks.html">BACKPACKS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="StarBags.html">STAR BAGS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="CorkPrint.html">CORK PRINT</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="straps.html">STRAP</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="toys.html">ACCESSORIES</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">SALE</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">TRADE SHOWS</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="main-content py-4">
    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
          <!-- Categories -->
          <div class="sidebar-section">
            <h5 class="sidebar-title">Categories</h5>
            <ul class="sidebar-list">
              <li><a href="#"><i class="fas fa-circle"></i> New in</a></li>
              <li><a href="allProducts.html"><i class="fas fa-circle"></i> HANDBAGS</a></li>
              <li><a href="BackPacks.html"><i class="fas fa-circle"></i> Backpacks</a></li>
              <li><a href="StarBags.html"><i class="fas fa-circle"></i> STAR BAGS</a></li>
              <li><a href="CorkPrint.html"><i class="fas fa-circle"></i> CORK PRINT</a></li>
              <li><a href="straps.html"><i class="fas fa-circle"></i> STRAP</a></li>
              <li><a href="toys.html"><i class="fas fa-circle"></i> ACCESSORIES</a></li>
              <li><a href="#"><i class="fas fa-circle"></i> SALE</a></li>
            </ul>
          </div>

          <!-- Popular Tags -->
          <div class="sidebar-section">
            <h5 class="sidebar-title">Popular tags</h5>
            <div class="tag-cloud">
              <span class="tag">camera</span>
              <span class="tag">camera bag</span>
              <span class="tag">camera bags</span>
              <span class="tag">hessian</span>
              <span class="tag">sling</span>
              <span class="tag">sling bag</span>
              <span class="tag">sling bags</span>
              <span class="tag">straps</span>
              <span class="tag">straps camera bags</span>
              <span class="tag">woven bags</span>
            </div>
          </div>
        </div>

        <!-- Registration Form -->
        <div class="col-md-9">
          <div class="register-form-container">
            <h2 class="register-title">REGISTER</h2>

            <form id="registration-form">
              <!-- Personal Details -->
              <div class="form-section">
                <h4 class="section-title">YOUR PERSONAL DETAILS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Gender</label>
                    <div class="gender-options">
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="gender" id="male" value="male">
                        <label class="form-check-label" for="male">Male</label>
                      </div>
                      <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="gender" id="female" value="female">
                        <label class="form-check-label" for="female">Female</label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="first-name" class="form-label">First name <span class="required">*</span></label>
                    <input type="text" class="form-control" id="first-name" required>
                  </div>
                  <div class="col-md-6">
                    <label for="last-name" class="form-label">Last name <span class="required">*</span></label>
                    <input type="text" class="form-control" id="last-name" required>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="dob" class="form-label">Date of birth</label>
                    <div class="row">
                      <div class="col-4">
                        <select class="form-select" id="dob-day">
                          <option value="">Day</option>
                          <!-- Days 1-31 will be populated by JavaScript -->
                        </select>
                      </div>
                      <div class="col-4">
                        <select class="form-select" id="dob-month">
                          <option value="">Month</option>
                          <!-- Months will be populated by JavaScript -->
                        </select>
                      </div>
                      <div class="col-4">
                        <select class="form-select" id="dob-year">
                          <option value="">Year</option>
                          <!-- Years will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="email" class="form-label">Email <span class="required">*</span></label>
                    <input type="email" class="form-control email-field" id="email" placeholder="<EMAIL>" required>
                  </div>
                </div>
              </div>

              <!-- Company Details -->
              <div class="form-section">
                <h4 class="section-title">COMPANY DETAILS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="company-name" class="form-label">Company name</label>
                    <input type="text" class="form-control" id="company-name">
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="vat-number" class="form-label">VAT number</label>
                    <input type="text" class="form-control" id="vat-number">
                    <small class="form-text text-muted">NOTE: Enter VAT number with country code e.g. GB ***********</small>
                  </div>
                </div>
              </div>

              <!-- Address Section -->
              <div class="form-section">
                <h4 class="section-title">YOUR ADDRESS</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="street-address" class="form-label">Street address</label>
                    <input type="text" class="form-control" id="street-address">
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="zip-code" class="form-label">Zip/postal code</label>
                    <input type="text" class="form-control" id="zip-code">
                  </div>
                  <div class="col-md-6">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" id="city">
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="form-section">
                <h4 class="section-title">YOUR CONTACT INFORMATION</h4>

                <div class="row mb-3">
                  <div class="col-12">
                    <label for="phone" class="form-label">Phone <span class="required">*</span></label>
                    <input type="tel" class="form-control" id="phone" required>
                  </div>
                </div>
              </div>

              <!-- Options -->
              <div class="form-section">
                <h4 class="section-title">OPTIONS</h4>

                <div class="form-check mb-3">
                  <input class="form-check-input" type="checkbox" id="newsletter" checked>
                  <label class="form-check-label" for="newsletter">
                    Newsletter
                  </label>
                </div>
              </div>

              <!-- Password Section -->
              <div class="form-section">
                <h4 class="section-title">YOUR PASSWORD</h4>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="password" class="form-label">Password <span class="required">*</span></label>
                    <input type="password" class="form-control password-field" id="password" placeholder="••••••••" required>
                  </div>
                  <div class="col-md-6">
                    <label for="confirm-password" class="form-label">Confirm password <span class="required">*</span></label>
                    <input type="password" class="form-control" id="confirm-password" required>
                  </div>
                </div>
              </div>

              <!-- reCAPTCHA -->
              <div class="form-section">
                <div class="recaptcha-container mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="recaptcha" required>
                    <label class="form-check-label" for="recaptcha">
                      I'm not a robot
                    </label>
                  </div>
                  <div class="recaptcha-logo">
                    <small class="text-muted">reCAPTCHA</small>
                    <small class="text-muted">Privacy - Terms</small>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="form-section">
                <button type="submit" class="btn btn-danger register-btn">REGISTER</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Newsletter Section -->
  <section class="newsletter-section py-5">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="newsletter-content d-flex align-items-center justify-content-between">
            <div class="newsletter-text">
              <h4 class="text-uppercase mb-0">NEWSLETTER</h4>
              <span class="newsletter-subtitle">Enter Your Email Address</span>
            </div>
            <div class="newsletter-form d-flex">
              <input type="email" class="form-control newsletter-input" placeholder="Enter Your Email Address" aria-label="Email">
              <button class="btn newsletter-btn text-uppercase" type="button">SUBSCRIBE</button>
            </div>
            <div class="social-links">
              <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"><i class="fab fa-google-plus-g"></i></a>
              <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
              <a href="#" class="social-link"><i class="fas fa-rss"></i></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark text-light py-5">
    <div class="container">
      <div class="row">
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">MY ACCOUNT</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">My Account</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">CUSTOMER SERVICE</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">News</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">INFORMATION</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Terms & Conditions</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">OUR OFFERS</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Search</a></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Initialize Firebase with your config
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "610655836965",
      appId: "1:610655836965:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Populate date dropdowns
    function populateDateDropdowns() {
      const daySelect = document.getElementById('dob-day');
      const monthSelect = document.getElementById('dob-month');
      const yearSelect = document.getElementById('dob-year');

      // Populate days
      for (let i = 1; i <= 31; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        daySelect.appendChild(option);
      }

      // Populate months
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];
      months.forEach((month, index) => {
        const option = document.createElement('option');
        option.value = index + 1;
        option.textContent = month;
        monthSelect.appendChild(option);
      });

      // Populate years (from current year down to 100 years ago)
      const currentYear = new Date().getFullYear();
      for (let i = currentYear; i >= currentYear - 100; i--) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i;
        yearSelect.appendChild(option);
      }
    }

    const registrationForm = document.getElementById('registration-form');

    registrationForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Form submitted'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;

      // Validate password confirmation
      if (password !== confirmPassword) {
        alert('Passwords do not match!');
        return;
      }

      // Check reCAPTCHA
      if (!document.getElementById('recaptcha').checked) {
        alert('Please verify that you are not a robot.');
        return;
      }

      console.log('Attempting to create user with email:', email); // Debug log

      try {
        // Create user with email and password
        const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;

        // Update user profile with full name
        await user.updateProfile({
          displayName: `${firstName} ${lastName}`
        });

        console.log('User created successfully:', user); // Debug log

        // Use our auth util to handle the login
        if (window.authUtils) {
          window.authUtils.loginUser(user);
        }

        alert('Registration successful!');
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Registration error:', error); // Debug log
        alert('Registration error: ' + error.message);
      }
    });

    // Check if user is already logged in
    window.addEventListener('DOMContentLoaded', () => {
      populateDateDropdowns();

      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });
  </script>

</body>
</html>
