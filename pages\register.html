<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - LUXORA</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/register.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body>
  <!-- Header placeholder -->
  <!-- <div id="header-placeholder"></div> -->

  <div class="registration-container">
    <div class="registration-image-section">
      <img src="../images/single-image-2.jpg" alt="Stylish Chair">
    </div>

    <div class="registration-form-section">
      <div class="logo">LUXORA</div>
      <h1>Register</h1>
      <p>Join us to explore 10,000+ products across 400+ categories</p>

      <!-- <button class="google-btn" id="googleSignUp">
        <img src="https://img.icons8.com/color/48/000000/google-logo.png" alt="Google logo">
        Sign Up with Google
      </button> -->

      <div class="or-separator"><span>OR</span></div>

      <form id="registration-form">
        <div class="input-group">
          <label for="full-name">Full Name</label>
          <input type="text" id="full-name" placeholder="John Canny" required>
        </div>

        <div class="input-group">
          <label for="email">Email</label>
          <input type="email" id="email" placeholder="<EMAIL>" required>
        </div>

        <div class="input-group">
          <label for="password">Password</label>
          <div class="input-field-container">
            <input type="password" id="password" placeholder="Enter your password" required>
            <span class="password-toggle" onclick="togglePasswordVisibility('password')">👁️</span>
          </div>
        </div>

        <div class="options">
          <div class="remember-me">
            <input type="checkbox" id="remember-me">
            <label for="remember-me">Remember Me</label>
          </div>
          <a href="#" class="forgot-password">Need help?</a>
        </div>

        <button type="submit" class="signup-btn">Sign Up</button>
      </form>

      <p class="login-redirect">
        Already have an account? <a href="login.html">Login</a>
      </p>
    </div>
  </div>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Script to fetch and inject header -->
  <script>
    // Fetch and inject the header
    fetch('header.html')
      .then(response => response.text())
      .then(html => {
        const placeholder = document.getElementById('header-placeholder');
        placeholder.innerHTML = html;
        
        // Fix navigation links for subpages
        const navLinks = placeholder.querySelectorAll('.navbar-nav a');
        navLinks.forEach(link => {
          // Update the Home link to point to root
          if (link.textContent.trim() === 'Home') {
            link.setAttribute('href', '../index.html');
          }
          
          // Remove "pages/" prefix from links if they have it
          const href = link.getAttribute('href');
          if (href && href.startsWith('pages/')) {
            link.setAttribute('href', href.replace('pages/', ''));
          }
        });

        // Fix user account links
        const accountLinks = placeholder.querySelectorAll('.col-lg-auto a');
        accountLinks.forEach(link => {
          const href = link.getAttribute('href');
          if (href && href.startsWith('pages/')) {
            link.setAttribute('href', href.replace('pages/', ''));
          } else if (href === 'index.html') {
            link.setAttribute('href', '../index.html');
          }
        });

        // Re-initialize Bootstrap components if needed
        if (typeof bootstrap !== 'undefined') {
          const offcanvasEl = document.getElementById('offcanvasCart');
          if (offcanvasEl) new bootstrap.Offcanvas(offcanvasEl);
        }
      })
      .catch(console.error);
  </script>

  <script>
    // Initialize Firebase with your config
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    const registrationForm = document.getElementById('registration-form');
    const googleSignUpButton = document.getElementById('googleSignUp');

    registrationForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Form submitted'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const fullName = document.getElementById('full-name').value;

      console.log('Attempting to create user with email:', email); // Debug log

      try {
        // Create user with email and password
        const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        // Update user profile with full name
        await user.updateProfile({
          displayName: fullName
        });

        console.log('User created successfully:', user); // Debug log
        
        // Use our auth util to handle the login
        window.authUtils.loginUser(user);
        
        alert('Registration successful!');
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Registration error:', error); // Debug log
        alert('Registration error: ' + error.message);
      }
    });

    // Google Sign Up (same as sign in but redirects differently)
    googleSignUpButton.addEventListener('click', async () => {
      const provider = new firebase.auth.GoogleAuthProvider();
      try {
        const result = await firebase.auth().signInWithPopup(provider);
        const user = result.user;
        
        console.log('Google sign-up successful:', user);
        
        // Use our auth util to handle the login
        window.authUtils.loginUser(user);
        
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Google sign-up error:', error);
        alert('Google sign-up failed: ' + error.message);
      }
    });

    // Check if user is already logged in
    window.addEventListener('DOMContentLoaded', () => {
      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });

    function togglePasswordVisibility(fieldId) {
      const passwordField = document.getElementById(fieldId);
      const toggleIcon = passwordField.nextElementSibling;
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.textContent = '🔒';
      } else {
        passwordField.type = 'password';
        toggleIcon.textContent = '👁️';
      }
    }
  </script>

  <!-- Footer placeholder -->
  <!-- <div id="footer-placeholder"></div> -->
</body>
</html>
