from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import stripe

app = FastAPI()

# Allow CORS for local development (adjust origins as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to your frontend domain in production!
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

stripe.api_key = "sk_test_51Rmv23AcfswtzgLBCCUPTq8vYnTtbx1e1gjR4uyYt3cfJBuRX9GxBWJrhAzTMm7eTAf8FDsyO7Q2UtBQrYoKdkij00mlH56Uv0"  # Your Stripe Secret Key

@app.post("/create-checkout-session")
async def create_checkout_session(request: Request):
    data = await request.json()
    try:
        session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[
                {
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': 'Sample Product',
                        },
                        'unit_amount': 2000,  # $20.00
                    },
                    'quantity': 1,
                },
            ],
            mode='payment',
            success_url='http://localhost:8000/success',
            cancel_url='http://localhost:8000/cancel',
        )
        return JSONResponse({"sessionId": session.id})
    except Exception as e:
        return JSONResponse({"error": str(e)}, status_code=400) 