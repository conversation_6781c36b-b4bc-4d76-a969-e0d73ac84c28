// Initialize the cart as an empty array
let cart = JSON.parse(localStorage.getItem("cart")) || [];
let cartDropdown = null;
let stylesLoaded = false;

// Function to add styles first (before any rendering)
function ensureStyles() {
  if (stylesLoaded) return;

  if (!document.querySelector("#cart-notification-styles")) {
    const style = document.createElement("style");
    style.id = "cart-notification-styles";
    style.textContent = `
            .cart-toast{position:fixed;bottom:24px;right:24px;min-width:260px;max-width:360px;padding:14px 16px;border-radius:12px;color:#fff;box-shadow:0 10px 30px rgba(0,0,0,.15);z-index:2000;backdrop-filter:saturate(180%) blur(6px);animation:toastIn .35s ease-out;}
            .cart-toast.success{background:linear-gradient(135deg,#27ae60,#1f9a52)}
            .cart-toast.error{background:linear-gradient(135deg,#e74c3c,#c0392b)}
            .cart-toast__content{display:flex;align-items:center;gap:10px}
            .cart-toast__icon{display:flex;align-items:center;justify-content:center;width:26px;height:26px;border-radius:50%;background:rgba(255,255,255,.2);font-weight:700}
            .cart-toast__text{flex:1;font-weight:500}
            .cart-toast__close{background:transparent;border:0;color:#fff;font-size:20px;line-height:1;cursor:pointer}
            .cart-toast__bar{height:3px;background:rgba(255,255,255,.4);margin-top:8px;position:relative;overflow:hidden;border-radius:2px}
            .cart-toast__bar:after{content:"";position:absolute;left:0;top:0;height:100%;width:0;background:#fff;animation:toastBar 3s linear forwards}
            @keyframes toastIn{from{transform:translateY(10px);opacity:0}to{transform:translateY(0);opacity:1}}
            @keyframes toastOut{from{transform:translateY(0);opacity:1}to{transform:translateY(10px);opacity:0}}
            @keyframes toastBar{from{width:0}to{width:100%}}
            
            /* Cart Dropdown Styles */
            .cart-dropdown-container {
                position: relative;
                display: inline-block;
            }
            .cart-dropdown {
                position: absolute;
                top: 100%;
                right: 0;
                width: 400px;
                max-height: 600px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                margin-top: 10px;
                overflow: hidden;
            }
            .cart-dropdown.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            .cart-dropdown-inner {
                max-height: 600px;
                overflow-y: auto;
            }
            .cart-header {
                background: linear-gradient(135deg, #ff6b6b, #ee5a6f);
                color: white;
                padding: 16px 20px;
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 18px;
                font-weight: 600;
                position: sticky;
                top: 0;
                z-index: 10;
            }
            .cart-header svg {
                width: 28px;
                height: 28px;
            }
            .cart-summary {
                background: #fff8f0;
                padding: 12px 20px;
                border-left: 4px solid #ff6b6b;
                margin: 16px 20px;
                border-radius: 4px;
                font-weight: 500;
                color: #333;
            }
            .cart-item {
                display: flex;
                gap: 16px;
                padding: 16px 20px;
                border-bottom: 1px solid #f0f0f0;
                transition: background 0.2s;
            }
            .cart-item:hover {
                background: #fafafa;
            }
            .cart-item-image {
                width: 80px;
                height: 80px;
                object-fit: cover;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                background: #f5f5f5;
                flex-shrink: 0;
            }
            .cart-item-details {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                min-width: 0;
            }
            .cart-item-name {
                font-weight: 600;
                color: #ff6b6b;
                margin-bottom: 6px;
                font-size: 15px;
            }
            .cart-item-color {
                color: #666;
                font-size: 13px;
                margin-bottom: 4px;
            }
            .cart-item-quantity {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-top: 4px;
            }
            .cart-item-quantity span {
                color: #666;
                font-size: 13px;
            }
            .cart-item-price {
                font-weight: 600;
                color: #333;
                font-size: 15px;
                align-self: center;
                flex-shrink: 0;
            }
            .cart-subtotal {
                padding: 20px;
                border-top: 2px solid #f0f0f0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 18px;
                font-weight: 700;
                background: white;
                position: sticky;
                bottom: 0;
            }
            .cart-subtotal-label {
                color: #333;
            }
            .cart-subtotal-amount {
                color: #ff6b6b;
                font-size: 20px;
            }
            .cart-actions {
                padding: 0 20px 20px;
                display: flex;
                flex-direction: column;
                gap: 12px;
                background: white;
            }
            .cart-btn {
                padding: 14px 24px;
                border: none;
                border-radius: 6px;
                font-weight: 600;
                font-size: 15px;
                cursor: pointer;
                transition: all 0.3s;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .cart-btn-primary {
                background: linear-gradient(135deg, #ff6b6b, #ee5a6f);
                color: white;
            }
            .cart-btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(238, 90, 111, 0.4);
            }
            .cart-btn-secondary {
                background: #fff;
                color: #ff6b6b;
                border: 2px solid #ff6b6b;
            }
            .cart-btn-secondary:hover {
                background: #ff6b6b;
                color: white;
            }
            .cart-empty {
                padding: 60px 20px;
                text-align: center;
                color: #999;
            }
            .cart-empty svg {
                width: 80px;
                height: 80px;
                margin-bottom: 20px;
                opacity: 0.3;
            }
            
            @media (max-width: 576px) {
                .cart-dropdown {
                    width: 340px;
                    right: -20px;
                }
            }
        `;
    document.head.appendChild(style);
  }

  stylesLoaded = true;
}

// Function to show notification
function showCartNotification(message, type = "success") {
  ensureStyles(); // Make sure styles are loaded

  const notification = document.createElement("div");
  notification.className = `cart-toast ${type}`;
  notification.innerHTML = `
        <div class="cart-toast__content">
            <div class="cart-toast__icon">${
              type === "success" ? "✓" : "!"
            }</div>
            <div class="cart-toast__text">${message}</div>
            <button class="cart-toast__close" aria-label="Close">&times;</button>
        </div>
        <div class="cart-toast__bar"></div>
    `;

  document.body.appendChild(notification);

  // Auto-remove after 3 seconds
  setTimeout(() => {
    notification.style.animation = "toastOut .35s ease-in forwards";
    setTimeout(() => notification.remove(), 350);
  }, 3000);

  // Close button functionality
  notification
    .querySelector(".cart-toast__close")
    .addEventListener("click", () => {
      notification.remove();
    });
}

// Function to save cart to localStorage
function saveCart() {
  localStorage.setItem("cart", JSON.stringify(cart));
}

// Function to create cart dropdown
function createCartDropdown() {
  if (cartDropdown) return;

  ensureStyles(); // Ensure styles are loaded before creating dropdown

  cartDropdown = document.createElement("div");
  cartDropdown.className = "cart-dropdown";
  cartDropdown.id = "cartDropdown";

  // Find the cart info container
  const cartInfo = document.querySelector(".cart-info");
  if (!cartInfo) return;

  // Wrap the cart link in a container if not already wrapped
  let container = cartInfo.closest(".cart-dropdown-container");
  if (!container) {
    container = document.createElement("div");
    container.className = "cart-dropdown-container";
    cartInfo.parentNode.insertBefore(container, cartInfo);
    container.appendChild(cartInfo);
  }

  container.appendChild(cartDropdown);

  // Add hover events
  container.addEventListener("mouseenter", () => {
    cartDropdown.classList.add("show");
  });

  container.addEventListener("mouseleave", () => {
    cartDropdown.classList.remove("show");
  });
}

// Function to render the cart in the dropdown
function renderCart() {
  const cartCounts = document.querySelectorAll(".cart-count");
  const badge = document.querySelector(".offcanvas .badge.bg-primary");

  if (!cartDropdown) {
    createCartDropdown();
  }

  if (!cartDropdown) return; // Exit if dropdown element doesn't exist

  let total = 0;
  const count = cart.reduce((sum, item) => sum + item.quantity, 0);

  // Update all cart counts
  cartCounts.forEach((el) => (el.textContent = `(${count})`));
  if (badge) badge.textContent = count;

  if (cart.length === 0) {
    cartDropdown.innerHTML = `
            <div class="cart-dropdown-inner">
                <div class="cart-header">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                    </svg>
                    Shopping cart (0) Total items
                </div>
                <div class="cart-empty">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <h5>Your cart is empty</h5>
                    <p>Add some items to get started!</p>
                </div>
            </div>
        `;
    return;
  }

  let cartHTML = `
        <div class="cart-dropdown-inner">
            <div class="cart-header">
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                </svg>
                Shopping cart (${count}) Total items
            </div>
            <div class="cart-summary">
                There are ${count} item(s) in your cart.
            </div>
            <div class="cart-items">
    `;

  cart.forEach((item) => {
    const itemTotal = item.price * item.quantity;
    total += itemTotal;

    cartHTML += `
            <div class="cart-item">
                <img src="${item.image}" alt="${
      item.name
    }" class="cart-item-image">
                <div class="cart-item-details">
                    <div class="cart-item-name">${item.name}</div>
                    ${
                      item.color
                        ? `<div class="cart-item-color">Colour: ${item.color}</div>`
                        : ""
                    }
                    <div class="cart-item-quantity">
                        <span>${item.quantity}</span>
                        <span>£${item.price.toFixed(2)} excl tax</span>
                    </div>
                </div>
                <div class="cart-item-price">£${itemTotal.toFixed(2)}</div>
            </div>
        `;
  });

  cartHTML += `
            </div>
            <div class="cart-subtotal">
                <span class="cart-subtotal-label">Sub-Total:</span>
                <span class="cart-subtotal-amount">£${total.toFixed(
                  2
                )} excl tax</span>
            </div>
            <div class="cart-actions">
                <button class="cart-btn cart-btn-primary" onclick="window.location.href='/cart'">
                    GO TO CART
                </button>
                <button class="cart-btn cart-btn-secondary" onclick="window.location.href='../pages/checkout'">
                    CHECKOUT
                </button>
            </div>
        </div>
    `;

  cartDropdown.innerHTML = cartHTML;
}

// Function to render the cart on the checkout page
function renderCheckoutCart() {
  const checkoutCartList = document.getElementById("checkoutCartList");

  if (!checkoutCartList) return; // Exit if checkout cart element doesn't exist

  checkoutCartList.innerHTML = "";
  let total = 0;

  cart.forEach((item) => {
    const li = document.createElement("li");
    li.className = "list-group-item d-flex justify-content-between lh-sm";
    li.innerHTML = `
            <div>
                <h6 class="my-0">${item.name}</h6>
                <small class="text-muted">Quantity: ${item.quantity}</small>
            </div>
            <span class="text-muted">£${(item.price * item.quantity).toFixed(
              2
            )}</span>
        `;
    checkoutCartList.appendChild(li);
    total += item.price * item.quantity;
  });

  // Add promo code (if any) and total row
  const promoCodeLi = document.createElement("li");
  promoCodeLi.className =
    "list-group-item d-flex justify-content-between bg-light";
  promoCodeLi.innerHTML = `
        <div class="text-success">
            <h6 class="my-0">Promo code</h6>
            <small>EXAMPLECODE</small>
        </div>
        <span class="text-success">−£5</span>
    `;
  checkoutCartList.appendChild(promoCodeLi);
  total -= 5; // Deduct promo code amount

  const totalLi = document.createElement("li");
  totalLi.className = "list-group-item d-flex justify-content-between";
  totalLi.innerHTML = `<span>Total (GBP)</span><strong>£${total.toFixed(
    2
  )}</strong>`;
  checkoutCartList.appendChild(totalLi);
}

// Cart functions
function addToCart(
  productId,
  productName,
  productPrice,
  productImage = "",
  productColor = ""
) {
  const existingItem = cart.find((item) => item.id === productId);
  if (existingItem) {
    existingItem.quantity += 1;
    showCartNotification(`Added another ${productName} to cart`);
  } else {
    cart.push({
      id: productId,
      name: productName,
      price: parseFloat(productPrice),
      quantity: 1,
      image: productImage,
      color: productColor,
    });
    showCartNotification(`${productName} added to cart`);
  }
  saveCart();
  renderCart(); // Update dropdown cart
  renderCheckoutCart(); // Update checkout cart
}

function removeFromCart(productId) {
  cart = cart.filter((item) => item.id !== productId);
  saveCart();
  renderCart(); // Update dropdown cart
  renderCheckoutCart(); // Update checkout cart
}

// Function to update the "Add to Cart" button color
function updateAddToCartButtonColor() {
  const addToCartButtons = document.querySelectorAll(".add-to-cart");
  addToCartButtons.forEach((button) => {
    button.classList.add("btn-primary");
  });
}

// Initialize cart functionality
function initCart() {
  ensureStyles(); // Load styles first before any rendering
  createCartDropdown();
  renderCart(); // Render dropdown cart on load
  renderCheckoutCart(); // Render checkout cart on load
  updateAddToCartButtonColor();

  // Event delegation for dynamically added elements
  document.body.addEventListener("click", function (e) {
    // Add to cart buttons
    if (e.target.closest(".add-to-cart")) {
      const btn = e.target.closest(".add-to-cart");
      addToCart(
        btn.dataset.productId,
        btn.dataset.productName,
        btn.dataset.productPrice,
        btn.dataset.productImage || "",
        btn.dataset.productColor || ""
      );
    }

    // Remove from cart buttons
    if (e.target.closest(".remove-from-cart")) {
      const btn = e.target.closest(".remove-from-cart");
      removeFromCart(btn.getAttribute("data-product-id"));
    }
  });
}

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initCart);
} else {
  initCart();
}
