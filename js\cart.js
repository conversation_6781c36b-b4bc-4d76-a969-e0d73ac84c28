// Initialize the cart as an empty array
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Function to show notification
function showCartNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `cart-toast ${type}`;
    notification.innerHTML = `
        <div class="cart-toast__content">
            <div class="cart-toast__icon">${type === 'success' ? '✓' : '!'}</div>
            <div class="cart-toast__text">${message}</div>
            <button class="cart-toast__close" aria-label="Close">&times;</button>
        </div>
        <div class="cart-toast__bar"></div>
    `;
    
    // Add styles dynamically if they don't exist
    if (!document.querySelector('#cart-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'cart-notification-styles';
        style.textContent = `
            .cart-toast{position:fixed;bottom:24px;right:24px;min-width:260px;max-width:360px;padding:14px 16px;border-radius:12px;color:#fff;box-shadow:0 10px 30px rgba(0,0,0,.15);z-index:2000;backdrop-filter:saturate(180%) blur(6px);animation:toastIn .35s ease-out;}
            .cart-toast.success{background:linear-gradient(135deg,#27ae60,#1f9a52)}
            .cart-toast.error{background:linear-gradient(135deg,#e74c3c,#c0392b)}
            .cart-toast__content{display:flex;align-items:center;gap:10px}
            .cart-toast__icon{display:flex;align-items:center;justify-content:center;width:26px;height:26px;border-radius:50%;background:rgba(255,255,255,.2);font-weight:700}
            .cart-toast__text{flex:1;font-weight:500}
            .cart-toast__close{background:transparent;border:0;color:#fff;font-size:20px;line-height:1;cursor:pointer}
            .cart-toast__bar{height:3px;background:rgba(255,255,255,.4);margin-top:8px;position:relative;overflow:hidden;border-radius:2px}
            .cart-toast__bar:after{content:"";position:absolute;left:0;top:0;height:100%;width:0;background:#fff;animation:toastBar 3s linear forwards}
            @keyframes toastIn{from{transform:translateY(10px);opacity:0}to{transform:translateY(0);opacity:1}}
            @keyframes toastOut{from{transform:translateY(0);opacity:1}to{transform:translateY(10px);opacity:0}}
            @keyframes toastBar{from{width:0}to{width:100%}}
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'toastOut .35s ease-in forwards';
        setTimeout(() => notification.remove(), 350);
    }, 3000);
    
    // Close button functionality
    notification.querySelector('.cart-toast__close').addEventListener('click', () => {
        notification.remove();
    });
}

// Function to save cart to localStorage
function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

// Function to render the cart in the offcanvas
function renderCart() {
    const cartList = document.querySelector('.offcanvas .list-group.mb-3');
    const cartCounts = document.querySelectorAll('.cart-count');
    const badge = document.querySelector('.offcanvas .badge.bg-primary');
    
    if (!cartList) return; // Exit if offcanvas cart element doesn't exist

    cartList.innerHTML = '';
    let total = 0;

    cart.forEach(item => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between lh-sm align-items-center';
        li.innerHTML = `
            <div>
                <h6 class="my-0">${item.name}</h6>
                <small class="text-body-secondary">Quantity: ${item.quantity}</small>
            </div>
            <div class="d-flex align-items-center">
                <span class="text-body-secondary me-3">£${(item.price * item.quantity).toFixed(2)}</span>
                <button class="btn btn-sm btn-outline-danger remove-from-cart" data-product-id="${item.id}">Remove</button>
            </div>
        `;
        cartList.appendChild(li);
        total += item.price * item.quantity;
    });

    // Add total row
    const totalLi = document.createElement('li');
    totalLi.className = 'list-group-item d-flex justify-content-between';
    totalLi.innerHTML = `<span>Total (GBP)</span><strong>£${total.toFixed(2)}</strong>`;
    cartList.appendChild(totalLi);

    // Update all cart counts
    const count = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCounts.forEach(el => el.textContent = `(${count})`);
    if (badge) badge.textContent = count;
}

// Function to render the cart on the checkout page
function renderCheckoutCart() {
    const checkoutCartList = document.getElementById('checkoutCartList');

    if (!checkoutCartList) return; // Exit if checkout cart element doesn't exist

    checkoutCartList.innerHTML = '';
    let total = 0;

    cart.forEach(item => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between lh-sm';
        li.innerHTML = `
            <div>
                <h6 class="my-0">${item.name}</h6>
                <small class="text-muted">Quantity: ${item.quantity}</small>
            </div>
            <span class="text-muted">£${(item.price * item.quantity).toFixed(2)}</span>
        `;
        checkoutCartList.appendChild(li);
        total += item.price * item.quantity;
    });

    // Add promo code (if any) and total row
    // For now, using a static promo code example from the original HTML
    // In a real application, this would be dynamic based on applied promo codes
    const promoCodeLi = document.createElement('li');
    promoCodeLi.className = 'list-group-item d-flex justify-content-between bg-light';
    promoCodeLi.innerHTML = `
        <div class="text-success">
            <h6 class="my-0">Promo code</h6>
            <small>EXAMPLECODE</small>
        </div>
        <span class="text-success">−£5</span>
    `;
    checkoutCartList.appendChild(promoCodeLi);
    total -= 5; // Deduct promo code amount

    const totalLi = document.createElement('li');
    totalLi.className = 'list-group-item d-flex justify-content-between';
    totalLi.innerHTML = `<span>Total (GBP)</span><strong>£${total.toFixed(2)}</strong>`;
    checkoutCartList.appendChild(totalLi);
}

// Cart functions
function addToCart(productId, productName, productPrice) {
    const existingItem = cart.find(item => item.id === productId);
    if (existingItem) {
        existingItem.quantity += 1;
        showCartNotification(`Added another ${productName} to cart`);
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: parseFloat(productPrice),
            quantity: 1
        });
        showCartNotification(`${productName} added to cart`);
    }
    saveCart();
    renderCart(); // Update offcanvas cart
    renderCheckoutCart(); // Update checkout cart
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCart();
    renderCart(); // Update offcanvas cart
    renderCheckoutCart(); // Update checkout cart
}

// Function to update the "Add to Cart" button color
function updateAddToCartButtonColor() {
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.classList.add('btn-primary');
    });
}

// Initialize cart functionality
function initCart() {
    renderCart(); // Render offcanvas cart on load
    renderCheckoutCart(); // Render checkout cart on load
    updateAddToCartButtonColor();
    
    // Event delegation for dynamically added elements
    document.body.addEventListener('click', function(e) {
        // Add to cart buttons
        if (e.target.closest('.add-to-cart')) {
            const btn = e.target.closest('.add-to-cart');
            // Removed the data-processed check to allow multiple clicks
            addToCart(btn.dataset.productId, btn.dataset.productName, btn.dataset.productPrice);
        }
        
        // Remove from cart buttons
        if (e.target.closest('.remove-from-cart')) {
            const btn = e.target.closest('.remove-from-cart');
            removeFromCart(btn.getAttribute('data-product-id'));
        }
    });
}

// Initialize when DOM is ready
if (document.readyState !== 'loading') {
    initCart();
} else {
    document.addEventListener('DOMContentLoaded', initCart);
}
