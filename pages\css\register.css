/* Register page styles to match the design */
body {
  font-family: 'Jo<PERSON>', sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* Top bar styling */
.top-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.language-select {
  border: none;
  background: transparent;
  font-size: 14px;
  max-width: 120px;
}

.right-menu a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.right-menu a:hover {
  color: #007bff;
}

/* Main navbar styling */
.main-navbar {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.search-bar .form-control {
  border-right: none;
  border-radius: 4px 0 0 4px;
  padding: 12px 15px;
}

.search-bar .btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
}

/* Logo styling - circular design with ATS text */
.logo-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border: 2px solid #666;
}

.logo-text {
  color: white;
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 1px;
}

.cart-info a {
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Bottom navigation styling */
.bottom-nav {
  background-color: #333;
  padding: 0;
}

.bottom-nav .navbar-nav {
  width: 100%;
  justify-content: center;
}

.bottom-nav .nav-link {
  color: white !important;
  padding: 15px 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  border-right: 1px solid #555;
}

.bottom-nav .nav-link:hover {
  background-color: #555;
}

.bottom-nav .nav-item:last-child .nav-link {
  border-right: none;
}

/* Main content styling */
.main-content {
  background-color: white;
  min-height: 80vh;
}

/* Sidebar styling */
.sidebar-section {
  background-color: #dc3545;
  color: white;
  padding: 20px;
  border-radius: 8px;
}

.sidebar-title {
  background-color: #dc3545;
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  text-transform: uppercase;
}

.sidebar-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-list li {
  margin-bottom: 10px;
}

.sidebar-list a {
  color: white;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-list a:hover {
  color: #f8f9fa;
}

.sidebar-list i {
  font-size: 8px;
}

/* Tag cloud styling */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-decoration: none;
}

.tag:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Registration form styling */
.register-form-container {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.register-title {
  color: #333;
  font-weight: 600;
  font-size: 24px;
  margin-bottom: 30px;
  text-transform: uppercase;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  color: #333;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.form-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.required {
  color: #dc3545;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  background-color: white;
}

.email-field {
  background-color: #e3f2fd;
}

.password-field {
  background-color: #e3f2fd;
}

.form-select {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

.gender-options {
  display: flex;
  gap: 20px;
}

.form-check-input {
  margin-top: 0.25rem;
}

.form-check-label {
  font-size: 14px;
  color: #666;
}

.form-text {
  font-size: 12px;
  color: #666;
}

/* reCAPTCHA styling */
.recaptcha-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recaptcha-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Button styling */
.register-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 30px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
}

.register-btn:hover {
  background-color: #c82333;
}
