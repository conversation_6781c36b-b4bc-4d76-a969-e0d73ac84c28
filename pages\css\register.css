/* Register page styles to match the design */
body {
  font-family: 'Jo<PERSON>', sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* Top bar styling */
.top-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.language-select {
  border: none;
  background: transparent;
  font-size: 14px;
  max-width: 120px;
}

.right-menu a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.right-menu a:hover {
  color: #007bff;
}

/* Main navbar styling */
.main-navbar {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.search-bar .form-control {
  border-right: none;
  border-radius: 4px 0 0 4px;
  padding: 12px 15px;
}

.search-bar .btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
}

/* Logo styling */
.logo h2 {
  color: #333;
  font-weight: bold;
  font-size: 24px;
  margin: 0;
}

.cart-info a {
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Bottom navigation styling */
.bottom-nav {
  background-color: #333;
  padding: 0;
}

.bottom-nav .navbar-nav {
  width: 100%;
  justify-content: center;
}

.bottom-nav .nav-link {
  color: white !important;
  padding: 15px 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  border-right: 1px solid #555;
}

.bottom-nav .nav-link:hover {
  background-color: #555;
}

.bottom-nav .nav-item:last-child .nav-link {
  border-right: none;
}

/* Main content styling */
.main-content {
  background-color: white;
  min-height: 80vh;
}

/* Sidebar styling */
.sidebar-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  background-color: white;
}

.sidebar-title {
  background-color: #dc3545;
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  padding: 15px 20px;
  text-transform: uppercase;
}

.sidebar-list {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: white;
}

.sidebar-list li {
  border-bottom: 1px solid #e9ecef;
}

.sidebar-list li:last-child {
  border-bottom: none;
}

.sidebar-list a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  transition: background-color 0.3s;
}

.sidebar-list a:hover {
  background-color: #f8f9fa;
  color: #dc3545;
}

.sidebar-list i {
  font-size: 12px;
  color: #666;
}

/* Tag cloud styling */
.tag-cloud {
  padding: 20px;
  background-color: white;
}

.tag {
  display: inline-block;
  color: #333;
  padding: 2px 4px;
  font-size: 12px;
  text-decoration: none;
  margin-right: 8px;
  margin-bottom: 5px;
}

.tag:hover {
  color: #dc3545;
}

.tag::after {
  content: ",";
  color: #666;
}

.tag:last-child::after {
  content: "";
}

/* Registration form styling */
.register-form-container {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.register-title {
  color: #333;
  font-weight: 600;
  font-size: 24px;
  margin-bottom: 30px;
  text-transform: uppercase;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  color: #333;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.form-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.required {
  color: #dc3545;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  background-color: white;
}

.email-field {
  background-color: #e3f2fd;
}

.password-field {
  background-color: #e3f2fd;
}

.form-select {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

.gender-options {
  display: flex;
  gap: 20px;
}

.form-check-input {
  margin-top: 0.25rem;
}

.form-check-label {
  font-size: 14px;
  color: #666;
}

.form-text {
  font-size: 12px;
  color: #666;
}

/* reCAPTCHA styling */
.recaptcha-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recaptcha-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Button styling */
.register-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 30px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
}

.register-btn:hover {
  background-color: #c82333;
}

/* Newsletter section */
.newsletter-section {
  background-color: #e9ecef;
  border-top: 1px solid #ddd;
}

.newsletter-content {
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.newsletter-text h4 {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.newsletter-subtitle {
  color: #666;
  font-size: 14px;
}

.newsletter-form {
  flex: 1;
  max-width: 400px;
  margin: 0 30px;
}

.newsletter-input {
  border-right: none;
  border-radius: 4px 0 0 4px;
  border: 1px solid #ddd;
}

.newsletter-btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
  background-color: #333;
  border-color: #333;
  color: white;
  padding: 12px 20px;
  font-size: 14px;
}

.newsletter-btn:hover {
  background-color: #555;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  color: #666;
  font-size: 16px;
  transition: color 0.3s;
  text-decoration: none;
}

.social-link:hover {
  color: #333;
}

/* Footer styling */
footer {
  background-color: #333 !important;
}

footer h5 {
  font-weight: 600;
  font-size: 16px;
}

footer ul li a {
  color: #ccc;
  font-size: 14px;
  transition: color 0.3s;
}

footer ul li a:hover {
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-navbar .row {
    text-align: center;
  }

  .main-navbar .col-lg-4,
  .main-navbar .col-lg-2,
  .main-navbar .col-lg-3 {
    margin-bottom: 15px;
  }

  .bottom-nav .navbar-nav {
    flex-direction: column;
  }

  .bottom-nav .nav-link {
    border-right: none;
    border-bottom: 1px solid #555;
  }

  .bottom-nav .nav-item:last-child .nav-link {
    border-bottom: none;
  }

  .newsletter-content {
    flex-direction: column;
    text-align: center;
  }

  .newsletter-form {
    margin: 20px 0;
    max-width: 100%;
  }

  .social-links {
    justify-content: center;
  }

  .register-form-container {
    padding: 20px;
  }

  .register-title {
    font-size: 20px;
  }

  .sidebar-section {
    margin-bottom: 30px;
  }
}
