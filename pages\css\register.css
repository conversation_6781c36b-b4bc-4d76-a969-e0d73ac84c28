:root {
  --primary-bg: #e8e1da;
  --secondary-bg: #8c7366;
  --card-bg: #fdfdfd;
  --text-color: #333;
  --light-text-color: #777;
  --border-color: #ddd;
  --button-bg: #8c7366;
  --button-hover-bg: #a18a7c;
  --google-button-border: #ccc;
}

body {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(to bottom right, var(--primary-bg), var(--secondary-bg));
  color: var(--text-color);
}

.registration-container {
  display: flex;
  background-color: var(--card-bg);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 900px; /* Adjust as needed */
  max-width: 90%;
  min-height: 550px; /* Adjust as needed */
}

/* Flex direction reversed for registration page */
.registration-image-section {
  flex: 1;
  background-color: var(--primary-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.registration-image-section img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.registration-form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.registration-form-section .logo {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 30px;
}

.registration-form-section h1 {
  font-size: 32px;
  margin-bottom: 10px;
  font-weight: 600;
}

.registration-form-section p {
  font-size: 14px;
  color: var(--light-text-color);
  margin-bottom: 30px;
}

.input-group {
  margin-bottom: 20px;
  width: 100%;
}

.input-group label {
  display: block;
  font-size: 14px;
  margin-bottom: 8px;
  color: var(--light-text-color);
}

.input-field-container {
  position: relative;
}

.input-group input {
  width: calc(100% - 20px);
  padding: 12px 10px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f8f8;
  outline: none;
}

.input-group input:focus {
  border-color: var(--button-bg);
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--light-text-color);
}

.google-btn {
  width: 100%;
  padding: 12px 20px;
  border: 1px solid var(--google-button-border);
  border-radius: 8px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  color: var(--text-color);
  margin-bottom: 25px;
  transition: background-color 0.2s;
}

.google-btn:hover {
  background-color: #f0f0f0;
}

.google-btn img {
  margin-right: 10px;
  width: 20px;
  height: 20px;
}

.or-separator {
  width: 100%;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
  line-height: 0.1em;
  margin: 20px 0;
  color: var(--light-text-color);
  font-size: 12px;
}

.or-separator span {
  background: var(--card-bg);
  padding: 0 10px;
}

.options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 14px;
  margin-top: -10px;
  margin-bottom: 30px;
}

.options .remember-me {
  display: flex;
  align-items: center;
}

.options .remember-me input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.options .forgot-password {
  color: var(--light-text-color);
  text-decoration: none;
}
.options .forgot-password:hover {
  text-decoration: underline;
}

.signup-btn { /* Changed from login-btn to signup-btn */
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  background-color: var(--button-bg);
  color: white;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.signup-btn:hover {
  background-color: var(--button-hover-bg);
}

/* Basic responsive adjustments */
@media (max-width: 768px) {
  .registration-container {
    flex-direction: column-reverse; /* Changed to column-reverse to put form on top for mobile */
    width: 90%;
    min-height: auto;
  }
  .registration-image-section {
    min-height: 250px;
    width: 100%;
  }
}
