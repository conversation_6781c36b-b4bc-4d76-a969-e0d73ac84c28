from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
from bs4 import BeautifulSoup
import csv
import json
import os

def login_to_site():
    try:
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--start-maximized')

        # Initialize the Chrome driver
        driver = webdriver.Chrome(options=chrome_options)
        print("Browser initialized...")

        # Navigate to login page
        login_url = "https://www.houseofmilano.com/login?ReturnUrl=%2fhandbags-2"
        driver.get(login_url)
        print("Navigated to login page...")

        # Wait for email input and enter credentials
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "Email"))
        )
        email_input.send_keys("<EMAIL>")

        # Find and fill password
        password_input = driver.find_element(By.ID, "Password")
        password_input.send_keys("123123")

        # Find and click login button
        login_button = driver.find_element(By.CSS_SELECTOR, "input[value='Log in']")
        login_button.click()
        print("Logged in successfully...")

        # Wait a bit for login to complete
        time.sleep(3)

        # Click on handbags link
        handbags_link = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Handbags')]"))
        )
        handbags_link.click()
        print("Navigated to handbags page...")

        # Wait for page to load
        time.sleep(5)
        print("Page loaded, extracting product information...")
        
        # Extract product information using BeautifulSoup
        extract_product_info(driver)

        # Keep browser open for further tasks
        return driver

    except Exception as e:
        print(f"An error occurred: {e}")
        if 'driver' in locals():
            driver.quit()
        return None

def extract_product_info(driver):
    try:
        # Get the page source
        html_content = driver.page_source
        
        # Parse HTML using BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find all product grid items - using the exact class as shown
        product_grids = soup.find_all('div', class_='item-box product-grid col-xs-12 col-sm-4 col-md-4 product-align')
        print(f"Found {len(product_grids)} products")
        
        # Prepare data for CSV
        products_data = []
        
        # Prepare data structure for products.js
        js_products_data = []
        
        # Extract data from each product
        for index, product in enumerate(product_grids, start=1):
            # Data for CSV
            product_data = {
                'id': str(index)
            }
            
            # Data for JS (will store as tuple of values)
            product_id = str(index)
            product_name = ''
            product_image = ''
            product_description = ''
            product_category = 'HANDBAGS'
            product_date = time.strftime("%b %d, %Y").upper()
            product_blog_title = ''
            product_price = 0.00  # Numeric price for JS
            
            # Find product-item div - using exact class and getting productid
            product_item = product.find('div', class_='product-item clearfix')
            
            if product_item:
                # Get product ID from data attribute if available
                if product_item.has_attr('data-productid'):
                    product_id = product_item['data-productid']
                    product_data['id'] = product_id
                
                # Extract image and product URL from "picture" div
                picture_div = product_item.find('div', class_='picture')
                if picture_div:
                    # Get product URL - look for <a> tag inside picture div
                    product_link = picture_div.find('a')
                    if product_link and product_link.has_attr('href'):
                        product_url = product_link['href']
                        product_data['url'] = product_url
                    
                    # Get product image
                    product_img = picture_div.find('img')
                    if product_img and product_img.has_attr('src'):
                        image_url = product_img['src']
                        product_data['image'] = image_url
                        product_image = image_url
                    else:
                        product_data['image'] = 'No image found'
                        product_image = 'No image found'
                
                # Extract details from "caption" div - using exact class
                caption_div = product_item.find('div', class_='caption')
                if caption_div:
                    # Find details div
                    details_div = caption_div.find('div', class_='details')
                    
                    if details_div:
                        # Extract product title from h3 tag - using exact tag and class
                        title_element = details_div.find('h3', class_='product-title')
                        if title_element and title_element.find('a'):
                            title = title_element.find('a').text.strip()
                            product_data['name'] = title
                            product_name = title
                            product_blog_title = title
                        else:
                            product_data['name'] = 'No title found'
                            product_name = 'No title found'
                            product_blog_title = 'No title found'
                    
                    # Extract prices - using exact class
                    prices_div = caption_div.find('div', class_='prices')
                    if prices_div:
                        # Look for span with class="price actual-price"
                        price_span = prices_div.find('span', class_='price actual-price')
                        if price_span:
                            price_text = price_span.text.strip()
                            product_data['price'] = price_text
                            
                            # Try to extract numeric price value for JS
                            try:
                                # Find price digits, removing any currency or text
                                price_digits = ''.join(filter(lambda x: x.isdigit() or x == '.', price_text))
                                product_price = float(price_digits)
                            except:
                                product_price = 0.00
                        else:
                            product_data['price'] = 'No price found'
                            product_price = 0.00
                    else:
                        product_data['price'] = 'No price found'
                        product_price = 0.00
                    
                    # Extract description - using exact tag and class
                    description_p = caption_div.find('p', class_='description')
                    if description_p:
                        description = description_p.text.strip()
                        product_data['description'] = description
                        product_description = description
                    else:
                        product_data['description'] = 'No description found'
                        product_description = 'No description found'
                    
                    # Add default values for other fields
                    product_data['category'] = product_category
                    product_data['date'] = product_date
                    product_data['blogTitle'] = product_data['name']
            
            # Add product data to CSV list
            products_data.append(product_data)
            
            # Add product data to JS list as a tuple
            js_products_data.append((
                product_id, 
                product_name, 
                product_image, 
                product_description, 
                product_category,
                product_date, 
                product_blog_title,
                product_price
            ))
            
            print(f"Extracted: {product_name}")
        
        # Save data to CSV
        save_to_csv(products_data)
        
        # Save data to products.js file
        save_to_js_file(js_products_data)
        
        print(f"Successfully extracted information for {len(products_data)} products")
        
    except Exception as e:
        print(f"Error extracting product information: {e}")

def save_to_csv(products_data):
    try:
        with open('handbags_products.csv', 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['id', 'name', 'description', 'price', 'image', 'url', 'category', 'date', 'blogTitle']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for product in products_data:
                writer.writerow(product)
            
        print(f"Data saved to handbags_products.csv")
    except Exception as e:
        print(f"Error saving to CSV: {e}")

def save_to_js_file(products_data):
    try:
        # Determine the path to products.js file
        script_dir = os.path.dirname(os.path.abspath(__file__))
        workspace_dir = os.path.dirname(script_dir)
        products_js_path = os.path.join(workspace_dir, 'js', 'products.js')
        
        # Format JavaScript array content
        js_content = "const products = [\n"
        
        for product in products_data:
            product_id, name, image, description, category, date, blog_title, price = product
            
            js_content += "  {\n"
            js_content += f'    id: "{product_id}",\n'
            js_content += f'    name: "{name.replace('"', '\\"')}",\n'
            js_content += f'    image: "{image}",\n'
            js_content += f'    description: "{description.replace('"', '\\"')}",\n'
            js_content += f'    category: "{category}",\n'
            js_content += f'    date: "{date}",\n'
            js_content += f'    blogTitle: "{blog_title.replace('"', '\\"')}",\n'
            js_content += f'    price: {price}\n'
            js_content += "  },\n"
            
        js_content += "];\n"
        
        # Write to products.js file
        with open(products_js_path, 'w', encoding='utf-8') as js_file:
            js_file.write(js_content)
            
        print(f"Data saved to {products_js_path}")
    except Exception as e:
        print(f"Error saving to products.js: {e}")

if __name__ == "__main__":
    driver = login_to_site()
    if driver:
        input("Press Enter to close the browser when done...")
        driver.quit()