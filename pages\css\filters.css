.filter-sidebar {
  width: 250px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-top: 4px solid #e74c3c; /* Red top bar like the image */
  padding: 20px;
  border-radius: 5px;
}

.sidebar-title {
  font-weight: 600;
  color: #e74c3c;
  text-transform: uppercase;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group label {
  font-weight: 500;
  margin-bottom: 5px;
  display: block;
}

.filter-sidebar input[type="text"],
.filter-sidebar input[type="number"] {
  border: 1px solid #ccc;
  font-size: 14px;
  border-radius: 4px;
}

.filter-sidebar button {
  font-size: 14px;
  padding: 6px 0;
}

/* ---------- Categories Sidebar ---------- */

.categories-section {
  border: 1px solid #ddd;
  border-top: 4px solid #e74c3c;
  border-radius: 4px;
  background: #fff;
  margin-bottom: 25px;
}

.categories-header {
  background-color: #e74c3c;
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  padding: 12px 15px;
  font-size: 15px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.categories-list {
  list-style: none;
  margin: 0;
  padding: 10px 0;
}

.categories-list > li {
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.categories-list > li:last-child {
  border-bottom: none;
}

.categories-list > li:hover {
  color: #e74c3c;
}

.categories-list > li.active {
  color: #e74c3c;
  font-weight: 600;
}

.categories-list i {
  font-size: 12px;
}
.categories-list a {
  color: inherit;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.categories-list a:hover {
  color: #e74c3c;
}

.subcategory-list {
  list-style: none;
  padding-left: 25px;
  margin-top: 8px;
  display: none;
}

.categories-list li.active .subcategory-list {
  display: block;
}

.subcategory-list li {
  padding: 5px 0;
  font-size: 13px;
  color: #555;
  cursor: pointer;
}

.subcategory-list li:hover {
  color: #e74c3c;
}
