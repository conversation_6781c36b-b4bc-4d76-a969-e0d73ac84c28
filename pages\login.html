<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - LUXORA</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/login.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body>
  <!-- Header placeholder -->
  <!-- <div id="header-placeholder"></div> -->

  <div class="login-container">
    <div class="login-form-section">
      <div class="logo">LUXORA</div>
      <h1>Login</h1>
      <p>Choose from 10,000+ products across 400+ categories</p>

      <!-- <button class="google-btn" id="googleSignIn">
        <img src="https://img.icons8.com/color/48/000000/google-logo.png" alt="Google logo">
        Sign in with Google
      </button> -->

      <div class="or-separator"><span>OR</span></div>

      <form id="login-form">
        <div class="input-group">
          <label for="email">Email</label>
          <input type="email" id="email" placeholder="<EMAIL>" required>
        </div>

        <div class="input-group">
          <label for="password">Password</label>
          <div class="input-field-container">
            <input type="password" id="password" placeholder="Enter your password" required>
            <span class="password-toggle" onclick="togglePasswordVisibility()">👁️</span>
          </div>
        </div>

        <div class="options">
          <div class="remember-me">
            <input type="checkbox" id="remember-me">
            <label for="remember-me">Remember Me</label>
          </div>
          <a href="#" class="forgot-password" id="forgotPassword">Forgot password?</a>
        </div>

        <button type="submit" class="login-btn">Login</button>
      </form>

      <p class="register-redirect">
        Don't have an account? <a href="register.html">Register</a>
      </p>
    </div>

    <div class="login-image-section">
      <img src="../images/single-image-2.jpg" alt="Stylish Chair">
    </div>
  </div>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Script to fetch and inject header -->
  <script>
    // Fetch and inject the header
    fetch('header.html')
      .then(response => response.text())
      .then(html => {
        const placeholder = document.getElementById('header-placeholder');
        placeholder.innerHTML = html;
        
        // Fix navigation links for subpages
        const navLinks = placeholder.querySelectorAll('.navbar-nav a');
        navLinks.forEach(link => {
          // Update the Home link to point to root
          if (link.textContent.trim() === 'Home') {
            link.setAttribute('href', '../index.html');
          }
          
          // Remove "pages/" prefix from links if they have it
          const href = link.getAttribute('href');
          if (href && href.startsWith('pages/')) {
            link.setAttribute('href', href.replace('pages/', ''));
          }
        });

        // Fix user account links
        const accountLinks = placeholder.querySelectorAll('.col-lg-auto a');
        accountLinks.forEach(link => {
          const href = link.getAttribute('href');
          if (href && href.startsWith('pages/')) {
            link.setAttribute('href', href.replace('pages/', ''));
          } else if (href === 'index.html') {
            link.setAttribute('href', '../index.html');
          }
        });

        // Re-initialize Bootstrap components if needed
        if (typeof bootstrap !== 'undefined') {
          const offcanvasEl = document.getElementById('offcanvasCart');
          if (offcanvasEl) new bootstrap.Offcanvas(offcanvasEl);
        }
      })
      .catch(console.error);
  </script>
  <script>
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Get form element
    const loginForm = document.getElementById('login-form');
    const googleSignInButton = document.getElementById('googleSignIn');
    const forgotPasswordLink = document.getElementById('forgotPassword');

    // Handle form submission
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Login attempt'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;

      try {
        const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
        const user = userCredential.user;
        console.log('Login successful:', user); // Debug log
        
        // Use our auth util to handle the login
        window.authUtils.loginUser(user);
        
        // Store user info in localStorage if remember-me is checked
        if (document.getElementById('remember-me').checked) {
          localStorage.setItem('userEmail', email);
        }

        // Redirect to main page
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Login error:', error); // Debug log
        alert('Login failed: ' + error.message);
      }
    });

    // Google Sign In
    googleSignInButton.addEventListener('click', async () => {
      const provider = new firebase.auth.GoogleAuthProvider();
      try {
        const result = await firebase.auth().signInWithPopup(provider);
        const user = result.user;
        console.log('Google sign-in successful:', user);
        
        // Use our auth util to handle the login
        window.authUtils.loginUser(user);
        
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Google sign-in error:', error);
        alert('Google sign-in failed: ' + error.message);
      }
    });

    // Forgot Password
    forgotPasswordLink.addEventListener('click', async (e) => {
      e.preventDefault();
      const email = document.getElementById('email').value;
      
      if (!email) {
        alert('Please enter your email address first');
        return;
      }

      try {
        await firebase.auth().sendPasswordResetEmail(email);
        alert('Password reset email sent! Please check your inbox.');
      } catch (error) {
        console.error('Password reset error:', error);
        alert('Password reset failed: ' + error.message);
      }
    });

    // Check for remembered email
    window.addEventListener('DOMContentLoaded', () => {
      const rememberedEmail = localStorage.getItem('userEmail');
      if (rememberedEmail) {
        document.getElementById('email').value = rememberedEmail;
        document.getElementById('remember-me').checked = true;
      }
      
      // Check if user is already logged in
      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });

    function togglePasswordVisibility() {
      const passwordField = document.getElementById('password');
      const toggleIcon = document.querySelector('.password-toggle');
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.textContent = '🔒';
      } else {
        passwordField.type = 'password';
        toggleIcon.textContent = '👁️';
      }
    }
  </script>

  <!-- Footer placeholder -->
  <div id="footer-placeholder"></div>

  
</body>
</html>
