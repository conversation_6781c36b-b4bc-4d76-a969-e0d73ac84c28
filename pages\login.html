<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - ATS Store</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Marcellus&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" type="text/css" href="../css/vendor.css">
  <link rel="stylesheet" type="text/css" href="../style.css">
  <link rel="stylesheet" href="css/login.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body class="homepage">
  <!-- Header placeholder -->
  <div id="header-placeholder"></div>

  <!-- Cart Offcanvas -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasCart" aria-labelledby="offcanvasCartLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasCartLabel">Shopping Cart</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <p>Your cart is empty</p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="container my-5">
    <div class="row justify-content-center">
      <div class="col-12">
        <p class="">WELCOME, PLEASE SIGN IN!</p>
      </div>
    </div>

    <div class="row justify-content-center">
      <!-- New Customer Section -->
      <div class="col-md-5 mb-4">
        <div class="card h-60">
          <div class="card-body p-4">
            <h4 class="card-title text-uppercase mb-3">NEW CUSTOMER</h4>
            <p class="card-text mb-4">By creating an account on our website you will be able to shop faster, be up to date on an orders status, and keep track of the orders you have previously made.</p>
            <a href="register.html" class="btn btn-danger text-uppercase px-4">REGISTER</a>
          </div>
        </div>
      </div>

      <!-- Returning Customer Section -->
      <div class="col-md-5 mb-4">
        <div class="card h-100">
          <div class="card-body p-4">
            <h4 class="card-title text-uppercase mb-3">RETURNING CUSTOMER</h4>

            <form id="login-form">
              <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              </div>

              <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                  <input type="password" class="form-control" id="password" placeholder="••••••••" required>
                  <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                  </button>
                </div>
              </div>

              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember-me">
                <label class="form-check-label" for="remember-me">Remember me?</label>
                <a href="#" class="float-end text-decoration-none" id="forgotPassword">Forgot password?</a>
              </div>

              <button type="submit" class="btn btn-danger text-uppercase px-4">LOG IN</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Newsletter Section -->
  <section class="newsletter bg-light py-5">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 text-center">
          <h4 class="text-uppercase mb-3">NEWSLETTER</h4>
          <p class="mb-4">Enter Your Email Address</p>
          <div class="input-group mb-3">
            <input type="email" class="form-control" placeholder="Enter Your Email Address" aria-label="Email">
            <button class="btn btn-dark text-uppercase" type="button">SUBSCRIBE</button>
          </div>
          <div class="social-links mt-4">
            <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
            <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
            <a href="#" class="me-3"><i class="fab fa-google-plus-g"></i></a>
            <a href="#" class="me-3"><i class="fab fa-youtube"></i></a>
            <a href="#"><i class="fas fa-rss"></i></a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark text-light py-5">
    <div class="container">
      <div class="row">
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">MY ACCOUNT</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">My Account</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">CUSTOMER SERVICE</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">News</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">INFORMATION</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Terms & Conditions</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">OUR OFFERS</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Search</a></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "610655836965",
      appId: "1:610655836965:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Get form element
    const loginForm = document.getElementById('login-form');
    const forgotPasswordLink = document.getElementById('forgotPassword');

    // Handle form submission
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Login attempt'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;

      try {
        const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
        const user = userCredential.user;
        console.log('Login successful:', user); // Debug log

        // Use our auth util to handle the login
        if (window.authUtils) {
          window.authUtils.loginUser(user);
        }

        // Store user info in localStorage if remember-me is checked
        if (document.getElementById('remember-me').checked) {
          localStorage.setItem('userEmail', email);
        }

        // Redirect to main page
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Login error:', error); // Debug log
        alert('Login failed: ' + error.message);
      }
    });

    // Forgot Password
    forgotPasswordLink.addEventListener('click', async (e) => {
      e.preventDefault();
      const email = document.getElementById('email').value;

      if (!email) {
        alert('Please enter your email address first');
        return;
      }

      try {
        await firebase.auth().sendPasswordResetEmail(email);
        alert('Password reset email sent! Please check your inbox.');
      } catch (error) {
        console.error('Password reset error:', error);
        alert('Password reset failed: ' + error.message);
      }
    });

    // Check for remembered email
    window.addEventListener('DOMContentLoaded', () => {
      const rememberedEmail = localStorage.getItem('userEmail');
      if (rememberedEmail) {
        document.getElementById('email').value = rememberedEmail;
        document.getElementById('remember-me').checked = true;
      }

      // Check if user is already logged in
      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });

    function togglePasswordVisibility() {
      const passwordField = document.getElementById('password');
      const toggleIcon = document.getElementById('password-toggle-icon');
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
      } else {
        passwordField.type = 'password';
        toggleIcon.className = 'fas fa-eye';
      }
    }
  </script>

  <!-- Script to fetch and inject header -->
  <script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Fetch and inject the header
      fetch('header.html')
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.text();
        })
        .then(html => {
          const placeholder = document.getElementById('header-placeholder');
          if (placeholder) {
            placeholder.innerHTML = html;

            // Fix navigation links for subpages
            const navLinks = placeholder.querySelectorAll('.navbar-nav a');
            navLinks.forEach(link => {
              const href = link.getAttribute('href');
              // Fix relative paths for subpages
              if (href && !href.startsWith('http') && !href.startsWith('../')) {
                if (href === 'index.html') {
                  link.setAttribute('href', '../index.html');
                } else if (!href.startsWith('#')) {
                  // Keep other page links as they are since we're in pages/ folder
                  // No change needed
                }
              }
            });

            // Re-initialize Bootstrap components
            if (typeof bootstrap !== 'undefined') {
              // Initialize offcanvas components
              const offcanvasElements = placeholder.querySelectorAll('.offcanvas');
              offcanvasElements.forEach(el => {
                new bootstrap.Offcanvas(el);
              });

              // Initialize navbar toggler
              const navbarToggler = placeholder.querySelector('.navbar-toggler');
              if (navbarToggler) {
                // Bootstrap handles this automatically, but we can add custom logic if needed
              }
            }

            console.log('Header loaded successfully');
          }
        })
        .catch(error => {
          console.error('Error loading header:', error);
          // Fallback: show a simple navigation
          const placeholder = document.getElementById('header-placeholder');
          if (placeholder) {
            placeholder.innerHTML = `
              <nav class="navbar navbar-expand-lg bg-light">
                <div class="container-fluid">
                  <a class="navbar-brand" href="../index.html">ATS Store</a>
                  <div class="navbar-nav">
                    <a class="nav-link" href="../index.html">Home</a>
                    <a class="nav-link" href="login.html">Login</a>
                    <a class="nav-link" href="register.html">Register</a>
                  </div>
                </div>
              </nav>
            `;
          }
        });
    });
  </script>

</body>
</html>
