<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - LUXORA</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Marcellus&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" type="text/css" href="../css/vendor.css">
  <link rel="stylesheet" type="text/css" href="../style.css">
  <link rel="stylesheet" href="css/login.css">
  <!-- Add Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <!-- Add auth.js -->
  <script src="../js/auth.js"></script>
</head>
<body class="homepage">
  <!-- Top Bar -->
  <div class="top-bar py-2">
    <div class="container-fluid">
      <div class="row justify-content-between align-items-center">
        <div class="col-auto">
          <select class="language-select form-select form-select-sm">
            <option selected>English</option>
          </select>
        </div>
        <div class="col-auto">
          <div class="d-flex gap-3 right-menu">
            <a href="register.html">
              <svg width="16" height="16" fill="currentColor" class="bi bi-person-plus" viewBox="0 0 16 16">
                <path d="M6 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664z"/>
                <path fill-rule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5"/>
              </svg>
              Register
            </a>
            <a><strong>|</strong></a>
            <a href="login.html">
              <svg width="16" height="16" fill="currentColor" class="bi bi-box-arrow-in-right" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0z"/>
                <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708z"/>
              </svg>
              Log In
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Navbar -->
  <div class="main-navbar py-3">
    <div class="container-fluid">
      <div class="row justify-content-between align-items-center g-3">
        <div class="col-lg-4 col-md-6">
          <div class="input-group search-bar">
            <input type="text" class="form-control" placeholder="Search store">
            <button class="btn btn-outline-secondary" type="button">
              <svg width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="col-lg-2 col-md-4">
          <div class="logo text-center">
            <a href="../index.html" class="text-decoration-none">
              <div class="logo-circle">
                <span class="logo-text">ATS</span>
              </div>
            </a>
          </div>
        </div>

        <div class="col-lg-3 col-md-5">
          <div class="cart-info d-flex align-items-center gap-2 justify-content-end">
            <a href="../index.html" class="text-uppercase mx-3">
              <svg width="23" height="23" fill="currentColor" class="bi bi-bag" viewBox="0 0 16 16">
                <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1m3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1z"/>
              </svg>
              <span class="pt-1">Shopping Cart <span class="cart-count">(0)</span></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Navigation -->
  <nav class="navbar navbar-expand-lg bottom-nav">
    <div class="container-fluid">
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="../index.html">HOME</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="allProducts.html">HANDBAGS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="BackPacks.html">BACKPACKS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="StarBags.html">STAR BAGS</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="CorkPrint.html">CORK PRINT</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="straps.html">STRAP</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="toys.html">ACCESSORIES</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">SALE</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">TRADE SHOWS</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container my-5">
    <div class="row justify-content-center">
      <div class="col-12">
        <h2 class="text-center mb-5 text-uppercase">WELCOME, PLEASE SIGN IN!</h2>
      </div>
    </div>

    <div class="row justify-content-center">
      <!-- New Customer Section -->
      <div class="col-md-5 mb-4">
        <div class="card h-100">
          <div class="card-body p-4">
            <h4 class="card-title text-uppercase mb-3">NEW CUSTOMER</h4>
            <p class="card-text mb-4">By creating an account on our website you will be able to shop faster, be up to date on an orders status, and keep track of the orders you have previously made.</p>
            <a href="register.html" class="btn btn-danger text-uppercase px-4">REGISTER</a>
          </div>
        </div>
      </div>

      <!-- Returning Customer Section -->
      <div class="col-md-5 mb-4">
        <div class="card h-100">
          <div class="card-body p-4">
            <h4 class="card-title text-uppercase mb-3">RETURNING CUSTOMER</h4>

            <form id="login-form">
              <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              </div>

              <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                  <input type="password" class="form-control" id="password" placeholder="••••••••" required>
                  <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                  </button>
                </div>
              </div>

              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember-me">
                <label class="form-check-label" for="remember-me">Remember me?</label>
                <a href="#" class="float-end text-decoration-none" id="forgotPassword">Forgot password?</a>
              </div>

              <button type="submit" class="btn btn-danger text-uppercase px-4">LOG IN</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Newsletter Section -->
  <section class="newsletter bg-light py-5">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 text-center">
          <h4 class="text-uppercase mb-3">NEWSLETTER</h4>
          <p class="mb-4">Enter Your Email Address</p>
          <div class="input-group mb-3">
            <input type="email" class="form-control" placeholder="Enter Your Email Address" aria-label="Email">
            <button class="btn btn-dark text-uppercase" type="button">SUBSCRIBE</button>
          </div>
          <div class="social-links mt-4">
            <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
            <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
            <a href="#" class="me-3"><i class="fab fa-google-plus-g"></i></a>
            <a href="#" class="me-3"><i class="fab fa-youtube"></i></a>
            <a href="#"><i class="fas fa-rss"></i></a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark text-light py-5">
    <div class="container">
      <div class="row">
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">MY ACCOUNT</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">My Account</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">CUSTOMER SERVICE</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">News</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">INFORMATION</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Terms & Conditions</a></li>
          </ul>
        </div>
        <div class="col-md-3 mb-4">
          <h5 class="text-uppercase mb-3">OUR OFFERS</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-light text-decoration-none">Search</a></li>
          </ul>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap Bundle JS (includes Popper) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyCRDmQz5zY24TfWw3Rt7O50WxRzCFHwEtw",
      authDomain: "ats-store-a6cbb.firebaseapp.com",
      projectId: "ats-store-a6cbb",
      storageBucket: "ats-store-a6cbb.firebasestorage.app",
      messagingSenderId: "610655836965",
      appId: "1:610655836965:web:aed5eaf62698cd3a98d8cc",
      measurementId: "G-437K17KYNY"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Get form element
    const loginForm = document.getElementById('login-form');
    const forgotPasswordLink = document.getElementById('forgotPassword');

    // Handle form submission
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Login attempt'); // Debug log

      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;

      try {
        const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
        const user = userCredential.user;
        console.log('Login successful:', user); // Debug log

        // Use our auth util to handle the login
        if (window.authUtils) {
          window.authUtils.loginUser(user);
        }

        // Store user info in localStorage if remember-me is checked
        if (document.getElementById('remember-me').checked) {
          localStorage.setItem('userEmail', email);
        }

        // Redirect to main page
        window.location.href = '../index.html';
      } catch (error) {
        console.error('Login error:', error); // Debug log
        alert('Login failed: ' + error.message);
      }
    });

    // Forgot Password
    forgotPasswordLink.addEventListener('click', async (e) => {
      e.preventDefault();
      const email = document.getElementById('email').value;

      if (!email) {
        alert('Please enter your email address first');
        return;
      }

      try {
        await firebase.auth().sendPasswordResetEmail(email);
        alert('Password reset email sent! Please check your inbox.');
      } catch (error) {
        console.error('Password reset error:', error);
        alert('Password reset failed: ' + error.message);
      }
    });

    // Check for remembered email
    window.addEventListener('DOMContentLoaded', () => {
      const rememberedEmail = localStorage.getItem('userEmail');
      if (rememberedEmail) {
        document.getElementById('email').value = rememberedEmail;
        document.getElementById('remember-me').checked = true;
      }

      // Check if user is already logged in
      if (window.authUtils && window.authUtils.isLoggedIn()) {
        window.location.href = '../index.html';
      }
    });

    function togglePasswordVisibility() {
      const passwordField = document.getElementById('password');
      const toggleIcon = document.getElementById('password-toggle-icon');
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
      } else {
        passwordField.type = 'password';
        toggleIcon.className = 'fas fa-eye';
      }
    }
  </script>

</body>
</html>
