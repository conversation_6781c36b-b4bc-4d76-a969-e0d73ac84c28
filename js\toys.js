// Toys product data
const toys = [
  {
    id: "15169406632315",
    name: "Super money spray gun big red",
    image:
      "https://abcimportsuk.com/cdn/shop/files/3315EDD9-1553-4C9E-ABB2-B649AFF16218.jpg?v=1752233163",
    description: "Super money spray gun big red",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 2000.0,
  },
  {
    id: "15166654218619",
    name: "Blue Single Microphone Karaoke",
    image:
      "https://abcimportsuk.com/cdn/shop/files/TY029752I-0.jpg?v=1751988533",
    description: "Blue Single Microphone Karaoke",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 2600.0,
  },
  {
    id: "15166627053947",
    name: "Pull back car",
    image:
      "https://abcimportsuk.com/cdn/shop/files/JSF230049I-999.jpg?v=1751988397",
    description: "Pull back car",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 600.0,
  },
  {
    id: "15166618894715",
    name: "Pull back car",
    image:
      "https://abcimportsuk.com/cdn/shop/files/JSF229919I-999.jpg?v=1751988291",
    description: "Pull back car",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166617616763",
    name: "Card Cobra",
    image:
      "https://abcimportsuk.com/cdn/shop/files/JSF213804I-999.jpg?v=1751988199",
    description: "Card Cobra",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166616174971",
    name: "DOLL Accessories",
    image:
      "https://abcimportsuk.com/cdn/shop/files/JSF053605I.jpg?v=1751988081",
    description: "DOLL Accessories",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 1400.0,
  },
  {
    id: "15166614143355",
    name: "Bow and Arrow",
    image:
      "https://abcimportsuk.com/cdn/shop/files/JSF014294-A.jpg?v=1751987994",
    description: "Bow and Arrow",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166613062011",
    name: "DOLL Dress Up Makeup Set",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY119911I-999.jpg?v=1751987907",
    description: "DOLL Dress Up Makeup Set",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 500.0,
  },
  {
    id: "15166609719675",
    name: "Seven-inch DOLL hair with swimming ring",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY119882I-999.jpg?v=1751987708",
    description: "Seven-inch DOLL hair with swimming ring",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166608572795",
    name: "11.5 inch suction board fashion doll",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY114823I-999.jpg?v=1751987604",
    description: "11.5 inch suction board fashion doll",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166607622523",
    name: "Slide Motorcycle (6 pieces)",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY092445I-999.jpg?v=1751987533",
    description: "Slide Motorcycle (6 pieces)",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166605525371",
    name: "Gliding airplane motorcycle (6 pieces)",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY092444I-999.jpg?v=1751987447",
    description: "Gliding airplane motorcycle (6 pieces)",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166603919739",
    name: "Bird Bow TOYS",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY092408I-999.jpg?v=1751987371",
    description: "Bird Bow TOYS",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166602051963",
    name: "Pizza SET",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY092398I-999.jpg?v=1751987279",
    description: "Pizza SET",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166599922043",
    name: "Soft Bullet Gun",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY086793I-999.jpg?v=1751987132",
    description: "Soft Bullet Gun",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166585471355",
    name: "DIY Hand-painted Tiger with Gas Pump",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY080495I-999.jpg?v=1751986227",
    description: "DIY Hand-painted Tiger with Gas Pump",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166531502459",
    name: "DIY Hand-painted Cow with Gas Pump",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY080489I-999.jpg?v=1751983460",
    description: "DIY Hand-painted Cow with Gas Pump",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15166530290043",
    name: "Intelligence building blocks (69pcs)",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY077738I-999.jpg?v=1751983354",
    description: "Intelligence building blocks (69pcs)",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 700.0,
  },
  {
    id: "15166527340923",
    name: "Beach Tools",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY074413I-999.jpg?v=1751983228",
    description: "Beach Tools",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15166526423419",
    name: "Military Set",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY069974I-999.jpg?v=**********",
    description: "Military Set",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 400.0,
  },
  {
    id: "15164422127995",
    name: "11.5 inch butterfly skirt doll",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY058068I-999.jpg?v=**********",
    description: "11.5 inch butterfly skirt doll",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 800.0,
  },
  {
    id: "15164417343867",
    name: "Suction board medical set play house",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY045481I-999.jpg?v=**********",
    description: "Suction board medical set play house",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 600.0,
  },
  {
    id: "15164386869627",
    name: "Mixing Engineering Wire Control Vehicle",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY054821I-999.jpg?v=**********",
    description: "Mixing Engineering Wire Control Vehicle",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 600.0,
  },
  {
    id: "15164363932027",
    name: "Play house tableware set",
    image:
      "https://abcimportsuk.com/cdn/shop/files/HJY045516I-999.jpg?v=**********",
    description: "Play house tableware set",
    category: "Activity Toys",
    date: null,
    blogTitle: null,
    price: 700.0,
  },
];

// Using the same rendering logic as allProducts.js
document.addEventListener("DOMContentLoaded", () => {
  const productList = document.getElementById("product-list");
  const paginationContainer = document.createElement("div");
  paginationContainer.classList.add(
    "pagination-container",
    "d-flex",
    "justify-content-center",
    "mt-4"
  );
  productList.parentElement.appendChild(paginationContainer);

  // Initial settings
  let itemsPerPage = 12;
  let currentPage = 1;
  let sortedProducts = [...toys];

  const sortSelect = document.getElementById("sortSelect");
  const displaySelect = document.getElementById("displaySelect");

  // ==========================
  // SORTING HANDLER
  // ==========================
  function applySorting() {
    const sortValue = sortSelect?.value;
    sortedProducts = [...toys]; // reset before sorting

    switch (sortValue) {
      case "priceLowHigh":
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case "priceHighLow":
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case "nameAZ":
        sortedProducts.sort((a, b) =>
          a.name.localeCompare(b.name, undefined, { sensitivity: "base" })
        );
        break;
      case "nameZA":
        sortedProducts.sort((a, b) =>
          b.name.localeCompare(a.name, undefined, { sensitivity: "base" })
        );
        break;
      case "newest":
        sortedProducts.sort(
          (a, b) =>
            new Date(b.date || "1970-01-01") - new Date(a.date || "1970-01-01")
        );
        break;
      default:
        break;
    }

    currentPage = 1;
    renderProducts(currentPage);
    renderPagination();
  }

  // ==========================
  // DISPLAY COUNT HANDLER
  // ==========================
  if (displaySelect) {
    displaySelect.addEventListener("change", () => {
      itemsPerPage = parseInt(displaySelect.value, 10);
      currentPage = 1;
      renderProducts(currentPage);
      renderPagination();
    });
  }

  if (sortSelect) {
    sortSelect.addEventListener("change", applySorting);
  }

  // ==========================
  // RENDER PRODUCTS
  // ==========================
  function renderProducts(page) {
    productList.innerHTML = "";

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex);

    // Determine base path for details page depending on location
    const detailsBase = window.location.pathname.includes("/pages/")
      ? ""
      : "pages/";

    paginatedProducts.forEach((product, index) => {
      const productColumn = document.createElement("div");
      productColumn.classList.add("product-grid-item");
      productColumn.setAttribute("data-aos", "fade-up");
      productColumn.setAttribute("data-aos-delay", `${index * 100}`);
      productColumn.innerHTML = `
        <article class="post-item product-item" 
          data-price="${product.price}" 
          data-product-id="${product.id}">
          <div class="post-image">
            <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
              <img src="${product.image}" alt="${product.name}" 
                   class="post-grid-image img-fluid">
            </a>
          </div>
          <div class="post-content">
            <div class="post-meta text-uppercase fs-6 text-secondary">
              <span class="post-category">${product.category} /</span>
              <span class="meta-day">${product.date ?? ""}</span>
            </div>
            <h5 class="post-title text-uppercase">
              <a class="product-link" href="${detailsBase}productDetails.html?id=${
        product.id
      }">
                ${product.name}
              </a>
            </h5>
            <p>${product.description}</p>
            <button class="add-to-cart btn btn-primary btn-sm" 
              data-product-id="${product.id}" 
              data-product-name="${product.name}" 
              data-product-price="${product.price}">
              Add to Cart - £${product.price ? product.price.toFixed(2) : "N/A"}
            </button>
          </div>
        </article>
      `;
      productList.appendChild(productColumn);
    });

    if (typeof AOS !== "undefined") AOS.refresh();

    if (typeof window.reinitializeFilters === "function") {
      setTimeout(() => window.reinitializeFilters(), 100);
    }
  }

  // ==========================
  // PAGINATION HANDLER
  // ==========================
  function renderPagination() {
    const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

    if (totalPages <= 1) {
      paginationContainer.innerHTML = "";
      return;
    }

    let paginationHTML = `
    <nav aria-label="Product navigation">
      <ul class="pagination">
        <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage - 1
          }" aria-label="Previous">
            <span aria-hidden="true">&laquo;</span>
          </a>
        </li>
  `;

    // ✅ Only show 3 pages at a time
    const maxVisiblePages = 3;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
      <li class="page-item ${currentPage === i ? "active" : ""}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
    }

    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? "disabled" : ""}">
          <a class="page-link" href="#" data-page="${
            currentPage + 1
          }" aria-label="Next">
            <span aria-hidden="true">&raquo;</span>
          </a>
        </li>
      </ul>
    </nav>
  `;

    paginationContainer.innerHTML = paginationHTML;

    // Add page navigation listeners
    const pageLinks = paginationContainer.querySelectorAll(".page-link");
    pageLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const newPage = parseInt(e.target.closest(".page-link").dataset.page);
        if (
          !isNaN(newPage) &&
          newPage !== currentPage &&
          newPage > 0 &&
          newPage <= totalPages
        ) {
          currentPage = newPage;
          renderProducts(currentPage);
          renderPagination();
          productList.scrollIntoView({ behavior: "smooth" });
        }
      });
    });
  }

  // ==========================
  // CACHE PRODUCT ON CLICK
  // ==========================
  document.addEventListener("click", function (e) {
    const link = e.target.closest("a.product-link");
    if (!link) return;

    const item = e.target.closest(".product-item");
    const id = item?.getAttribute("data-product-id");
    if (!id) return;

    const prod = toys.find((p) => String(p.id) === String(id));
    if (prod) {
      localStorage.setItem("lastClickedProduct", JSON.stringify(prod));
    } else {
      localStorage.setItem("lastClickedProduct", JSON.stringify({ id }));
    }
  });

  // ==========================
  // INITIAL RENDER
  // ==========================
  renderProducts(currentPage);
  renderPagination();
});
