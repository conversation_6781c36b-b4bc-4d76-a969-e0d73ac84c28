// Get the current page name from the URL
function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').pop().replace('.html', '');
    return page;
}

// Get the appropriate product array based on the page
function getProductArray() {
    const page = getCurrentPage();
    switch(page.toLowerCase()) {
        case 'backpacks':
            return window.backpackProducts || [];
        case 'corkprint':
            return window.corkPrintProducts || [];
        case 'starbags':
            return window.starBagProducts || [];
        case 'straps':
            return window.strapProducts || [];
        case 'toys':
            return window.toyProducts || [];
        case 'petitems':
            return window.petItemProducts || [];
        case 'allproducts':
            return window.products || [];
        default:
            return window.products || [];
    }
}

// Function to find a product by ID across all product arrays
function findProductById(productId) {
    const arrays = [
        window.products,
        window.backpackProducts,
        window.corkPrintProducts,
        window.starBagProducts,
        window.strapProducts,
        window.toyProducts,
        window.petItemProducts
    ];

    for (const arr of arrays) {
        if (arr) {
            const product = arr.find(p => p.id === productId);
            if (product) return product;
        }
    }
    return null;
}

// Function to show product in modal
function showProductModal(productId) {
    const product = findProductById(productId);
    if (!product) {
        console.error('Product not found:', productId);
        return;
    }

    // Update modal content
    document.getElementById('modalProductName').textContent = product.name;
    document.getElementById('modalProductPrice').textContent = `$${product.price.toFixed(2)}`;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalProductCategory').textContent = product.category || 'N/A';
    document.getElementById('modalProductId').textContent = product.id;
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductImage').alt = product.name;
    document.getElementById('modalQuantity').value = 1;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// Function to change quantity
function changeQuantity(delta) {
    const input = document.getElementById('modalQuantity');
    let value = parseInt(input.value) + delta;
    if (value < 1) value = 1;
    input.value = value;
}

// Function to handle add to cart
function addToCart() {
    const quantity = document.getElementById('modalQuantity').value;
    alert(`Added ${quantity} item(s) to cart`);
    const modalElement = document.getElementById('productModal');
    const modal = bootstrap.Modal.getInstance(modalElement);
    modal.hide();
}

// Create and inject modal HTML
function createProductModal() {
    const modalHTML = `
        <div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="productModalLabel"></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <img id="modalProductImage" src="" alt="" class="img-fluid rounded">
                            </div>
                            <div class="col-md-6">
                                <h3 id="modalProductName"></h3>
                                <p class="h4 mb-4" id="modalProductPrice"></p>
                                <p id="modalProductDescription"></p>
                                <div class="mb-3">
                                    <strong>Category:</strong>
                                    <span id="modalProductCategory"></span>
                                </div>
                                <div class="mb-3">
                                    <strong>Product ID:</strong>
                                    <span id="modalProductId"></span>
                                </div>
                                <div class="mb-3">
                                    <label for="modalQuantity" class="form-label">Quantity:</label>
                                    <div class="input-group" style="width: 130px;">
                                        <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">-</button>
                                        <input type="number" class="form-control text-center" id="modalQuantity" value="1" min="1">
                                        <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">+</button>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="addToCart()">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// Initialize modal and quantity controls
function initializeModal() {
    if (!document.getElementById('productModal')) {
        createProductModal();
    }
}

// Function to change quantity
function changeQuantity(delta) {
    const quantityInput = document.getElementById('modalQuantity');
    let newValue = parseInt(quantityInput.value) + delta;
    if (newValue < 1) newValue = 1;
    quantityInput.value = newValue;
}

// Function to show product modal
function showProductModal(productId) {
    const product = findProductById(productId);
    if (!product) return;

    document.getElementById('modalProductName').textContent = product.name;
    document.getElementById('modalProductPrice').textContent = `$${product.price.toFixed(2)}`;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalProductCategory').textContent = product.category;
    document.getElementById('modalProductId').textContent = product.id;
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductImage').alt = product.name;
    document.getElementById('modalQuantity').value = 1;
    
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// Function to find product by ID
function findProductById(productId) {
    const productArrays = [
        window.products,
        window.backpackProducts,
        window.corkPrintProducts,
        window.starBagProducts,
        window.strapProducts,
        window.toyProducts,
        window.petItemProducts
    ];

    for (const array of productArrays) {
        if (array) {
            const product = array.find(p => p.id === productId);
            if (product) return product;
        }
    }
    return null;
}

// Function to add to cart (you can implement this based on your cart functionality)
function addToCart() {
    alert('Product added to cart!');
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    modal.hide();
}

// Add modal styles
function addModalStyles() {
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
        .modal-img-container {
            max-height: 400px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-img-container img {
            max-height: 100%;
            object-fit: contain;
        }
        .quantity-input {
            width: 80px !important;
        }
        #productModal .modal-content {
            border-radius: 15px;
        }
        #productModal .modal-header {
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
            border-radius: 15px 15px 0 0;
        }
        #productModal .modal-body {
            padding: 2rem;
        }
        #productModal .product-price {
            color: #dc3545;
            font-weight: bold;
            font-size: 1.5rem;
        }
    `;
    document.head.appendChild(styleSheet);
}

document.addEventListener('DOMContentLoaded', () => {
    initializeModal();
    addModalStyles();
    
    const productList = document.getElementById('product-list');
    const paginationContainer = document.createElement('div');
    paginationContainer.classList.add('pagination-container', 'd-flex', 'justify-content-center', 'mt-4');
    if (productList) {
        productList.parentElement.appendChild(paginationContainer);
    }

    // Pagination settings
    const itemsPerPage = 30;
    let currentPage = 1;
    
    function renderProducts(page) {
        if (!productList) return;

        // Get the appropriate product array
        const productsToRender = getProductArray();
        console.log('Rendering products for page:', getCurrentPage(), 'Count:', productsToRender.length);
        
        // Clear existing products
        productList.innerHTML = '';
        
        // Calculate start and end indices
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedProducts = productsToRender.slice(startIndex, endIndex);
        
        // Determine base path for details page depending on current location
        const detailsBase = window.location.pathname.includes('/pages/') ? '' : 'pages/';

        // Render products for current page
        paginatedProducts.forEach((product, index) => {
            const productColumn = document.createElement('div');
            productColumn.classList.add('product-grid-item');
            productColumn.setAttribute('data-aos', 'fade-up');
            productColumn.setAttribute('data-aos-delay', `${index * 100}`);
            productColumn.innerHTML = `
                <article class="post-item product-item" data-price="${product.price}" data-product-id="${product.id}">
                    <div class="post-image">
                        <a class="product-link" href="${detailsBase}productDetails.html?id=${product.id}">
                            <img src="${product.image}" alt="${product.name}" class="post-grid-image img-fluid">
                        </a>
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">
                            <a class="product-link" href="${detailsBase}productDetails.html?id=${product.id}">${product.name}</a>
                        </h3>
                        <p class="post-description">${product.description}</p>
                        <div class="post-meta">
                            <span class="post-price">$${product.price.toFixed(2)}</span>
                        </div>
                    </div>
                </article>
            `;
            productList.appendChild(productColumn);
        });

        // Update pagination
        const totalPages = Math.ceil(productsToRender.length / itemsPerPage);
        updatePagination(totalPages, page);
    }

    function updatePagination(totalPages, currentPage) {
        if (!paginationContainer) return;
        
        paginationContainer.innerHTML = '';
        
        if (totalPages <= 1) return;

        const createPageButton = (pageNum, text) => {
            const button = document.createElement('button');
            button.className = `btn ${pageNum === currentPage ? 'btn-primary' : 'btn-outline-primary'} mx-1`;
            button.textContent = text || pageNum;
            button.addEventListener('click', () => {
                currentPage = pageNum;
                renderProducts(currentPage);
                window.scrollTo(0, 0);
            });
            return button;
        };

        // Previous button
        if (currentPage > 1) {
            paginationContainer.appendChild(createPageButton(currentPage - 1, '←'));
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (
                i === 1 || // First page
                i === totalPages || // Last page
                (i >= currentPage - 2 && i <= currentPage + 2) // Pages around current
            ) {
                paginationContainer.appendChild(createPageButton(i));
            } else if (
                i === currentPage - 3 ||
                i === currentPage + 3
            ) {
                // Add ellipsis
                const ellipsis = document.createElement('span');
                ellipsis.className = 'mx-2';
                ellipsis.textContent = '...';
                paginationContainer.appendChild(ellipsis);
            }
        }

        // Next button
        if (currentPage < totalPages) {
            paginationContainer.appendChild(createPageButton(currentPage + 1, '→'));
        }
    }

    // Initial render
    renderProducts(currentPage);
});
