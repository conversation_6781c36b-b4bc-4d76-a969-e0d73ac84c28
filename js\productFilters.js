// Create notification system
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="message">${message}</span>
            <button class="close-btn">&times;</button>
        </div>
    `;
    
    // Add styles dynamically
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 4px;
            z-index: 1000;
            animation: slideIn 0.5s ease-out;
        }
        .notification.success {
            background-color: #4CAF50;
            color: white;
        }
        .notification.error {
            background-color: #f44336;
            color: white;
        }
        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .close-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 20px;
        }
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    }, 3000);
    
    // Close button functionality
    notification.querySelector('.close-btn').addEventListener('click', () => {
        notification.remove();
    });
}

// Professional Product Filtering System
function initializeFilters() {
    const searchInput = document.getElementById('searchInput');
    const minPrice = document.getElementById('minPrice');
    const maxPrice = document.getElementById('maxPrice');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const resetFiltersBtn = document.getElementById('resetFilters');
    const productList = document.getElementById('product-list');
    const sortSelect = document.getElementById('sortSelect') || createSortSelect();

    // Store original products and their data for efficient filtering
    let originalProducts = [];
    let productData = new Map();
    
    // Create sort select if it doesn't exist
    function createSortSelect() {
        const select = document.createElement('select');
        select.id = 'sortSelect';
        select.className = 'form-select mb-3';
        select.innerHTML = `
            <option value="default">Default Sorting</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="name-asc">Name: A to Z</option>
            <option value="name-desc">Name: Z to A</option>
        `;
        const filterContainer = document.querySelector('.filter-container');
        if (filterContainer) {
            filterContainer.insertBefore(select, filterContainer.firstChild);
        }
        return select;
    }

    // Function to collect and cache all products
    function collectProducts() {
        if (originalProducts.length === 0) {
            const products = productList.querySelectorAll('.product-item');
            originalProducts = Array.from(products);
            
            // Cache product data for better performance
            originalProducts.forEach(product => {
                const id = product.dataset.productId;
                productData.set(id, {
                    price: getProductPrice(product),
                    name: getProductName(product),
                    element: product
                });
            });
        }
        return originalProducts;
    }

    // Enhanced price extraction function
    function getProductPrice(product) {
        const priceElement = product.querySelector('.price');
        if (!priceElement) return 0;
        const priceText = priceElement.textContent.trim();
        return parseFloat(priceText.replace(/[^0-9.]/g, '')) || 0;
    }

    // Enhanced product name extraction
    function getProductName(product) {
        const nameElement = product.querySelector('.product-title');
        return nameElement ? nameElement.textContent.trim().toLowerCase() : '';

    // Enhanced filtering and sorting function
    function filterProducts() {
        const products = collectProducts();
        const searchTerm = searchInput.value.toLowerCase().trim();
        const min = parseFloat(minPrice.value) || 0;
        const max = parseFloat(maxPrice.value) || Infinity;
        const sortValue = sortSelect.value;

        let visibleProducts = [];
        let debounceTimer;

        // Show loading state
        productList.classList.add('filtering');
        
        // Clear previous no-results message if it exists
        const existingNoResults = document.querySelector('.no-results-message');
        if (existingNoResults) {
            existingNoResults.remove();
        }

        // Filter products
        products.forEach(product => {
            const id = product.dataset.productId;
            const data = productData.get(id);
            
            if (!data) return;

            const matchesSearch = !searchTerm || 
                data.name.includes(searchTerm) || 
                product.dataset.tags?.toLowerCase().includes(searchTerm);
            
            const matchesPrice = data.price >= min && (max === Infinity || data.price <= max);
            
            if (matchesSearch && matchesPrice) {
                visibleProducts.push(data);
                product.style.display = '';
            } else {
                product.style.display = 'none';
            }
        });

        // Sort visible products
        visibleProducts.sort((a, b) => {
            switch(sortValue) {
                case 'price-low':
                    return a.price - b.price;
                case 'price-high':
                    return b.price - a.price;
                case 'name-asc':
                    return a.name.localeCompare(b.name);
                case 'name-desc':
                    return b.name.localeCompare(a.name);
                default:
                    return 0;
            }
        });

        // Apply sorting to DOM
        if (sortValue !== 'default') {
            visibleProducts.forEach(data => {
                productList.appendChild(data.element);
            });
        }

        // Show results feedback
        if (visibleProducts.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results-message alert alert-info text-center my-4';
            noResults.innerHTML = `
                <i class="fas fa-search mb-2"></i>
                <h5>No products found</h5>
                <p class="mb-0">Try adjusting your search criteria or filters</p>
                <button class="btn btn-outline-primary mt-3" onclick="document.getElementById('resetFilters').click()">
                    Reset Filters
                </button>
            `;
            productList.appendChild(noResults);
        } else {
            showNotification(`Found ${visibleProducts.length} products`);
        }

        // Remove loading state
        productList.classList.remove('filtering');
    }

    // Add loading styles
    const style = document.createElement('style');
    style.textContent = `
        .filtering {
            position: relative;
            min-height: 200px;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }
        .filtering::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .no-results-message {
            padding: 2rem;
        }
        .no-results-message i {
            font-size: 2rem;
            color: #6c757d;
            display: block;
        }
    `;
    document.head.appendChild(style);

    // Event listeners with debouncing
    let debounceTimer;
    
    function debounce(func, wait) {
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(debounceTimer);
                func(...args);
            };
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(later, wait);
        };
    }

    // Apply filters with debouncing
    const debouncedFilter = debounce(filterProducts, 300);

    applyFiltersBtn.addEventListener('click', filterProducts);
    
    resetFiltersBtn.addEventListener('click', () => {
        searchInput.value = '';
        minPrice.value = '';
        maxPrice.value = '';
        sortSelect.value = 'default';
        
        // Show all products with animation
        productList.classList.add('filtering');
        setTimeout(() => {
            collectProducts().forEach(product => {
                product.style.display = '';
            });
            productList.classList.remove('filtering');
            showNotification('Filters have been reset');
        }, 300);
    });

    // Real-time search and sort
    searchInput.addEventListener('input', debouncedFilter);
    sortSelect.addEventListener('change', filterProducts);
    
    // Price input validation and auto-filtering
    [minPrice, maxPrice].forEach(input => {
        input.addEventListener('input', (e) => {
            // Remove non-numeric characters except decimal point
            e.target.value = e.target.value.replace(/[^\d.]/g, '');
            
            // Ensure only one decimal point
            const parts = e.target.value.split('.');
            if (parts.length > 2) {
                e.target.value = parts[0] + '.' + parts.slice(1).join('');
            }
            
            debouncedFilter();
        });
    });
}

// Initialize filters when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeFilters);
