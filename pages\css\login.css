/* Login page styles to match the design */
body {
  font-family: 'Jo<PERSON>', sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* Ensure header placeholder is visible */
#header-placeholder {
  display: block !important;
  width: 100%;
}

/* Ensure all header sections are visible */
#header-placeholder .top-bar,
#header-placeholder .main-navbar,
#header-placeholder .bottom-nav {
  display: block !important;
}

/* Top bar styling */
.top-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.language-select {
  border: none;
  background: transparent;
  font-size: 14px;
  max-width: 120px;
}

.right-menu a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.right-menu a:hover {
  color: #007bff;
}

/* Main navbar styling */
.main-navbar {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.search-bar .form-control {
  border-right: none;
  border-radius: 4px 0 0 4px;
  padding: 12px 15px;
}

.search-bar .btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
}

/* Logo styling */
.logo {
  text-align: center;
}

.logo h2 {
  color: #333;
  font-weight: bold;
  font-size: 28px;
  letter-spacing: 2px;
  margin: 0;
  font-family: 'Marcellus', serif;
}

.logo a {
  color: #333;
  text-decoration: none;
}

.logo a:hover {
  color: #007bff;
}

/* Legacy logo styling - circular design with ATS text */
.logo-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border: 2px solid #666;
}

.logo-text {
  color: white;
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 1px;
}

.cart-info a {
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Bottom navigation styling */
.bottom-nav {
  background-color: #333;
  padding: 0;
}

.bottom-nav .navbar-nav {
  width: 100%;
  justify-content: center;
}

.bottom-nav .nav-link {
  color: white !important;
  padding: 15px 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  border-right: 1px solid #555;
}

.bottom-nav .nav-link:hover {
  background-color: #555;
}

.bottom-nav .nav-item:last-child .nav-link {
  border-right: none;
}

/* Main content styling */
.main-content {
  background-color: #f8f9fa;
  min-height: 60vh;
}

.container {
  max-width: 1200px;
}

.welcome-title {
  color: #333;
  font-weight: 600;
  font-size: 24px;
  margin-bottom: 40px;
}

/* Login card styling to match the design */
.login-card {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: 100%;
}

.card-title {
  color: #333;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 20px;
}

.card-text {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 25px;
}

/* Form styling */
.form-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  background-color: white;
}

.password-field {
  background-color: #e3f2fd;
}

.form-check-label {
  font-size: 14px;
  color: #666;
}

.forgot-link {
  color: #007bff;
  font-size: 14px;
}

.forgot-link:hover {
  color: #0056b3;
}

/* Button styling */
.register-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 30px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.register-btn:hover {
  background-color: #c82333;
}

.login-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 30px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  width: auto;
}

.login-btn:hover {
  background-color: #c82333;
}

/* Newsletter section */
.newsletter-section {
  background-color: #e9ecef;
  border-top: 1px solid #ddd;
}

.newsletter-content {
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.newsletter-text h4 {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.newsletter-subtitle {
  color: #666;
  font-size: 14px;
}

.newsletter-form {
  flex: 1;
  max-width: 400px;
  margin: 0 30px;
}

.newsletter-input {
  border-right: none;
  border-radius: 4px 0 0 4px;
  border: 1px solid #ddd;
}

.newsletter-btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
  background-color: #333;
  border-color: #333;
  color: white;
  padding: 12px 20px;
  font-size: 14px;
}

.newsletter-btn:hover {
  background-color: #555;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  color: #666;
  font-size: 16px;
  transition: color 0.3s;
  text-decoration: none;
}

.social-link:hover {
  color: #333;
}

/* Footer styling */
footer {
  background-color: #333 !important;
}

footer h5 {
  font-weight: 600;
  font-size: 16px;
}

footer ul li a {
  color: #ccc;
  font-size: 14px;
  transition: color 0.3s;
}

footer ul li a:hover {
  color: white;
}

/* Password toggle button */
.input-group .btn-outline-secondary {
  border-color: #ddd;
  color: #666;
}

.input-group .btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #ddd;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-navbar .row {
    text-align: center;
  }

  .main-navbar .col-lg-4,
  .main-navbar .col-lg-2,
  .main-navbar .col-lg-3 {
    margin-bottom: 15px;
  }

  .bottom-nav .navbar-nav {
    flex-direction: column;
  }

  .bottom-nav .nav-link {
    border-right: none;
    border-bottom: 1px solid #555;
  }

  .bottom-nav .nav-item:last-child .nav-link {
    border-bottom: none;
  }

  .newsletter-content {
    flex-direction: column;
    text-align: center;
  }

  .newsletter-form {
    margin: 20px 0;
    max-width: 100%;
  }

  .social-links {
    justify-content: center;
  }

  .login-card {
    padding: 20px;
  }

  .welcome-title {
    font-size: 20px;
  }
}
