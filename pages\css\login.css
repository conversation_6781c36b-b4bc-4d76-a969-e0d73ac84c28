/* Login page styles to match the design */
body {
  font-family: 'Jo<PERSON>', sans-serif;
  background-color: #f8f9fa;
}

/* Top bar styling */
.top-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.language-select {
  border: none;
  background: transparent;
  font-size: 14px;
  max-width: 120px;
}

.right-menu a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.right-menu a:hover {
  color: #007bff;
}

/* Main navbar styling */
.main-navbar {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.search-bar .form-control {
  border-right: none;
  border-radius: 4px 0 0 4px;
}

.search-bar .btn {
  border-left: none;
  border-radius: 0 4px 4px 0;
}

.logo img {
  max-height: 60px;
}

.cart-info a {
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Bottom navigation styling */
.bottom-nav {
  background-color: #333;
  padding: 0;
}

.bottom-nav .navbar-nav {
  width: 100%;
  justify-content: center;
}

.bottom-nav .nav-link {
  color: white !important;
  padding: 15px 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  border-right: 1px solid #555;
}

.bottom-nav .nav-link:hover {
  background-color: #555;
}

.bottom-nav .nav-item:last-child .nav-link {
  border-right: none;
}

/* Main content styling */
.container {
  max-width: 1200px;
}

/* Card styling for login sections */
.card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-title {
  color: #333;
  font-weight: 600;
  font-size: 18px;
}

.card-text {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* Form styling */
.form-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px 15px;
  font-size: 14px;
}

.form-control:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-check-label {
  font-size: 14px;
  color: #666;
}

/* Button styling */
.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  padding: 12px 30px;
  font-weight: 500;
  border-radius: 4px;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* Newsletter section */
.newsletter {
  background-color: #f8f9fa;
}

.newsletter h4 {
  font-weight: 600;
  color: #333;
}

.newsletter .input-group .form-control {
  border-right: none;
}

.newsletter .btn-dark {
  border-left: none;
  background-color: #333;
  border-color: #333;
}

.social-links a {
  color: #666;
  font-size: 18px;
  transition: color 0.3s;
}

.social-links a:hover {
  color: #333;
}

/* Footer styling */
footer {
  background-color: #333 !important;
}

footer h5 {
  font-weight: 600;
  font-size: 16px;
}

footer ul li a {
  color: #ccc;
  font-size: 14px;
  transition: color 0.3s;
}

footer ul li a:hover {
  color: white;
}

/* Password toggle button */
.input-group .btn-outline-secondary {
  border-color: #ddd;
  color: #666;
}

.input-group .btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #ddd;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-navbar .row {
    text-align: center;
  }

  .main-navbar .col-lg-4,
  .main-navbar .col-lg-2,
  .main-navbar .col-lg-3 {
    margin-bottom: 15px;
  }

  .bottom-nav .navbar-nav {
    flex-direction: column;
  }

  .bottom-nav .nav-link {
    border-right: none;
    border-bottom: 1px solid #555;
  }

  .bottom-nav .nav-item:last-child .nav-link {
    border-bottom: none;
  }
}
